<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <!-- Animation System Diagram -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#fff"/>
    </marker>
  </defs>
  
  <!-- Component Boxes -->
  <g class="components">
    <!-- Animation Manager -->
    <rect x="350" y="50" width="100" height="50" rx="5" 
          fill="#4a90e2" stroke="white" stroke-width="2">
      <animate attributeName="opacity" values="0.8;1;0.8" 
               dur="2s" repeatCount="indefinite"/>
    </rect>
    <text x="400" y="80" text-anchor="middle" fill="white">Animation Manager</text>
    
    <!-- Movement -->
    <rect x="200" y="150" width="100" height="50" rx="5" 
          fill="#50e3c2" stroke="white" stroke-width="2">
      <animate attributeName="opacity" values="0.8;1;0.8" 
               dur="2s" repeatCount="indefinite"/>
    </rect>
    <text x="250" y="180" text-anchor="middle" fill="white">Movement</text>
    
    <!-- Range Updates -->
    <rect x="350" y="150" width="100" height="50" rx="5" 
          fill="#f5a623" stroke="white" stroke-width="2">
      <animate attributeName="opacity" values="0.8;1;0.8" 
               dur="2s" repeatCount="indefinite"/>
    </rect>
    <text x="400" y="180" text-anchor="middle" fill="white">Range Updates</text>
    
    <!-- Status -->
    <rect x="500" y="150" width="100" height="50" rx="5" 
          fill="#b8e986" stroke="white" stroke-width="2">
      <animate attributeName="opacity" values="0.8;1;0.8" 
               dur="2s" repeatCount="indefinite"/>
    </rect>
    <text x="550" y="180" text-anchor="middle" fill="white">Status</text>
  </g>
  
  <!-- Connecting Lines -->
  <g class="connections" stroke="white" stroke-width="2" 
     marker-end="url(#arrowhead)">
    <line x1="400" y1="100" x2="250" y2="150"/>
    <line x1="400" y1="100" x2="400" y2="150"/>
    <line x1="400" y1="100" x2="550" y2="150"/>
  </g>
</svg>
