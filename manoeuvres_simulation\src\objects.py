# src/objects.py

import os
import random
# PyQt5 imports
from PyQt5.QtWidgets import (
    QGraphicsRectItem,
    QGraphicsPixmapItem,
    QGraphicsTextItem,
    QGraphicsEllipseItem,
    QGraphicsPolygonItem,
    QGraphicsLineItem
)
from PyQt5.QtGui import (
    QColor,
    QBrush,
    QPen,
    QPixmap,
    QPainter,
    QPolygonF,
    QPainterPath,
    QFont
)
from PyQt5.QtCore import Qt, QTimer, QPointF
from PyQt5.QtMultimedia import QSoundEffect
from PyQt5.QtCore import QUrl

# Icon size mapping by hierarchy
HIERARCHY_ICON_SIZES = {
    1: 25,  # Brigade
    2: 20, # Groupement
    3: 20,  # Bataillon
    4: 16, # Sous-groupement
    5: 16,  # Compagnie
    6: 13,  # Section
}

class MovablePixmapItem(QGraphicsPixmapItem):
    def __init__(self, parent_obj, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.parent_obj = parent_obj
        self.setFlag(QGraphicsPixmapItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsPixmapItem.ItemIsMovable, True)
        self.setFlag(QGraphicsPixmapItem.ItemSendsGeometryChanges, True)
        self.setZValue(10)  # Optional, for stacking order
        self._dragging = False  # Track drag state
        self._drag_offset = None  # For smooth drag

    def paint(self, painter, option, widget=None):
        """Custom paint to show selection highlight."""
        # Draw the pixmap first
        super().paint(painter, option, widget)

        # Draw selection highlight if selected
        if self.isSelected():
            painter.setPen(QPen(QColor("yellow"), 3, Qt.SolidLine))
            painter.setBrush(Qt.NoBrush)
            rect = self.boundingRect()
            painter.drawRect(rect.adjusted(-2, -2, 2, 2))

            # Add selection indicator text
            painter.setPen(QPen(QColor("yellow"), 1))
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            painter.drawText(rect.bottomLeft() + QPointF(0, 15), "SELECTED")

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._drag_offset = event.pos()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self._drag_offset is not None and event.buttons() & Qt.LeftButton:
            new_pos = event.scenePos() - self._drag_offset
            self.setPos(new_pos)
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        self._drag_offset = None
        super().mouseReleaseEvent(event)

    def itemChange(self, change, value):
        from PyQt5.QtWidgets import QGraphicsItem
        if change == QGraphicsItem.ItemPositionChange:
            if not self._dragging:
                self._dragging = True
                # Print initial position
                scene = self.scene()
                if scene and scene.views():
                    view = scene.views()[0]
                    main_win = view.parentWidget().window()
                    if hasattr(main_win, "get_xyz_from_scene_point"):
                        xyz = main_win.get_xyz_from_scene_point(self.pos())
                        # print(f"[DEBUG] (Initial) {self.parent_obj.name}: {xyz}")
        elif change == QGraphicsItem.ItemPositionHasChanged:
            self._dragging = False
            new_pos = self.pos()
            scene = self.scene()
            if scene and scene.views():
                view = scene.views()[0]
                main_win = view.parentWidget().window()
                if hasattr(main_win, "get_xyz_from_scene_point"):
                    xyz = main_win.get_xyz_from_scene_point(new_pos)
                    # print(f"[DEBUG] (Moved) {self.parent_obj.name}: {xyz}")
            # Update LOS live only if portée is enabled
            if hasattr(self, 'parent_obj'):
                scene = self.scene()
                if scene and scene.views():
                    view = scene.views()[0]
                    main_win = view.parentWidget().window()
                    if getattr(main_win, 'portee_enabled', True):
                        if hasattr(self.parent_obj, 'draw_los_polygon'):
                            self.parent_obj.draw_los_polygon()
                        if hasattr(self.parent_obj, 'draw_portee2_los_polygon'):
                            self.parent_obj.draw_portee2_los_polygon()
                        if hasattr(self.parent_obj, 'draw_portee3_los_polygon'):
                            self.parent_obj.draw_portee3_los_polygon()
        return super().itemChange(change, value)

class MovableRectItem(QGraphicsRectItem):
    def __init__(self, parent_obj, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.parent_obj = parent_obj
        self.setFlag(QGraphicsRectItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsRectItem.ItemIsMovable, True)
        self.setFlag(QGraphicsRectItem.ItemSendsGeometryChanges, True)
        self.setZValue(10)
        self._dragging = False  # Track drag state
        self._drag_offset = None  # For smooth drag

    def paint(self, painter, option, widget=None):
        """Custom paint to show selection highlight."""
        # Draw the rectangle first
        super().paint(painter, option, widget)

        # Draw selection highlight if selected
        if self.isSelected():
            painter.setPen(QPen(QColor("yellow"), 3, Qt.SolidLine))
            painter.setBrush(Qt.NoBrush)
            rect = self.boundingRect()
            painter.drawRect(rect.adjusted(-2, -2, 2, 2))

            # Add selection indicator text
            painter.setPen(QPen(QColor("yellow"), 1))
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            painter.drawText(rect.bottomLeft() + QPointF(0, 15), "SELECTED")

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._drag_offset = event.pos()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self._drag_offset is not None and event.buttons() & Qt.LeftButton:
            new_pos = event.scenePos() - self._drag_offset
            self.setPos(new_pos)
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        self._drag_offset = None
        super().mouseReleaseEvent(event)

    def itemChange(self, change, value):
        from PyQt5.QtWidgets import QGraphicsItem
        if change == QGraphicsItem.ItemPositionChange:
            if not self._dragging:
                self._dragging = True
                # Print initial position
                scene = self.scene()
                if scene and scene.views():
                    view = scene.views()[0]
                    main_win = view.parentWidget().window()
                    if hasattr(main_win, "get_xyz_from_scene_point"):
                        xyz = main_win.get_xyz_from_scene_point(self.pos())
                        # print(f"[DEBUG] (Initial) {self.parent_obj.name}: {xyz}")
        elif change == QGraphicsItem.ItemPositionHasChanged:
            self._dragging = False
            new_pos = self.pos()
            scene = self.scene()
            if scene and scene.views():
                view = scene.views()[0]
                main_win = view.parentWidget().window()
                if hasattr(main_win, "get_xyz_from_scene_point"):
                    xyz = main_win.get_xyz_from_scene_point(new_pos)
                    # print(f"[DEBUG] (Moved) {self.parent_obj.name}: {xyz}")
            # Update LOS live only if portée is enabled
            if hasattr(self, 'parent_obj'):
                scene = self.scene()
                if scene and scene.views():
                    view = scene.views()[0]
                    main_win = view.parentWidget().window()
                    if getattr(main_win, 'portee_enabled', True):
                        if hasattr(self.parent_obj, 'draw_los_polygon'):
                            self.parent_obj.draw_los_polygon()
                        if hasattr(self.parent_obj, 'draw_portee2_los_polygon'):
                            self.parent_obj.draw_portee2_los_polygon()
                        if hasattr(self.parent_obj, 'draw_portee3_los_polygon'):
                            self.parent_obj.draw_portee3_los_polygon()
        return super().itemChange(change, value)

class SimulationObject:
    def __init__(self, name, object_type, position, space, scene, icon_size=40, color=QColor("red")):
        self.los_update_counter = 10
        self.portee2_los_update_counter = 10
        self.portee3_los_update_counter = 10
        # Initialize health-related attributes first
        self.personnel_count = 0
        self.vehicle_count = 0
        self.mechanized_count = 0
        self.tank_count = 0
        self.name = name
        self.object_type = object_type
        self.scene = scene
        self.color = color
        self.icon_size = icon_size
        self.is_dynamic = False  # Not used, but kept for compatibility
        self.designation = name  # Default designation is name
        self.ap_radius_m = 400  # Formerly pov_radius_m (AP)
        self.ac_radius_m = 800  # Formerly portee2_radius_m (AC)
        self.missile_radius_m = 1600  # Formerly portee3_radius_m (Missile)
        self.portee1_visible = False
        self.portee2_visible = False
        self.portee3_visible = False
        self.create_graphic(position)
        self.create_label(position)
        self.los_polygon_item = None
        self.ap_radius_px = 100  # Will be set in set_ap_radius_by_meters
        self.ac_radius_px = 100  # Will be set in set_ac_radius_by_meters
        self.missile_radius_px = 100  # Will be set in set_missile_radius_by_meters
        self.portee2_los_polygon_item = None
        self.portee3_los_polygon_item = None
        self.set_ap_radius_by_meters(self.ap_radius_m)
        self.set_ac_radius_by_meters(self.ac_radius_m)
        self.set_missile_radius_by_meters(self.missile_radius_m)
        self.health_bar_item = None
        self.health_bar_visible = True  # Controlled by global toggle
        print(f"[DEBUG][INIT] {self.name}: init_health set to {self.total_health()}")
        self.create_health_bar()
        self.last_fire_time = {'AP': 0, 'AC': 0, 'Missile': 0}  # Track last fire time for each weapon type
        # Do NOT set init_health here!
        self.create_health_bar()
        self.last_fire_time = {'AP': 0, 'AC': 0, 'Missile': 0}  # Track last fire time for each weapon type
        self._initial_personnel_count = self.personnel_count
        self._initial_vehicle_count = self.vehicle_count
        self._initial_mechanized_count = self.mechanized_count
        self._initial_tank_count = self.tank_count
        
        # Speed characteristics
        self.base_speed = self._get_base_speed(object_type)  # Speed in meters per second
        self.current_speed = self.base_speed  # Current speed considering terrain
        self.terrain_factor = 1.0  # Multiplier for terrain effects (1.0 = normal)
        
    def _get_base_speed(self, object_type):
        """Define base speeds for different unit types in meters per second"""
        speed_table = {
            # Infantry units
            'Section_meca': 5,  # ~18 km/h on foot
            'Section_moto': 5,
            
            # Mechanized units
            'M113_ami': 12,    # ~43 km/h for APCs
            'M113_ennemi': 12,
            
            # Motorized units
            'Jeep_ami': 16,    # ~58 km/h for light vehicles
            'Jeep_ennemi': 16,
            
            # Armored units
            'Tank_ami': 14,    # ~50 km/h for tanks
            'Tank_ennemi': 14,
        }
        
        # Default speed if type not found (walking speed)
        return speed_table.get(object_type, 5)  # Default to 5 m/s

    def update_speed_for_terrain(self, terrain_complexity):
        """Update speed based on terrain complexity (0.0 to 1.0)"""
        # Terrain complexity of 0.0 means normal terrain (no slowdown)
        # Terrain complexity of 1.0 means maximum difficulty (significant slowdown)
        min_terrain_factor = 0.3  # Units will move at least 30% of their base speed
        self.terrain_factor = 1.0 - (terrain_complexity * (1.0 - min_terrain_factor))
        self.current_speed = self.base_speed * self.terrain_factor
        
    def get_travel_time(self, distance):
        """Calculate travel time in milliseconds based on distance and current speed"""
        if self.current_speed <= 0:
            return 15000  # Default duration if speed is invalid
        return int((distance / self.current_speed) * 1000)  # Convert to milliseconds

    def finalize_init_health(self):
        self.init_health = self.total_health()
        # Store initial counts for reset
        self._initial_personnel_count = self.personnel_count
        self._initial_vehicle_count = self.vehicle_count
        self._initial_mechanized_count = self.mechanized_count
        self._initial_tank_count = self.tank_count
        print(f"[DEBUG][INIT] {self.name}: init_health finalized to {self.init_health}")

    def create_graphic(self, position):
        # First, try the old hardcoded image_paths for legacy types
        image_paths = {
            "gpt_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'gpt_meca.png'),
            "gpt_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'gpt_moto.png'),
            "S_gpt_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'S_gpt_meca.png'),
            "S_gpt_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'S_gpt_moto.png'),
            "enmi_brigade": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'enmi_brigade.png'),
            "enmi_company": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'enmi_company.png'),
            "enmi_section": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'enmi_section.png'),
            "Bataillon_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_meca.png'),
            "Bataillon_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_moto.png'),
            "Brigade_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_meca.png'),
            "Brigade_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_moto.png'),
            "Compagnie_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_meca.png'),
            "Compagnie_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_moto.png'),
            "Groupement_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_meca.png'),
            "Groupement_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_moto.png'),
            "Section_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_meca.png'),
            "Section_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_moto.png'),
            "Sous_groupement_meca": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous_groupement_meca.png'),
            "Sous_groupement_moto": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous_groupement_moto.png'),
            "Bataillon_meca_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_meca_Ennemi.png'),
            "Bataillon_moto_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_moto_Ennemi.png'),
            "Brigade_meca_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_meca_Ennemi.png'),
            "Brigade_moto_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_moto_Ennemi.png'),
            "Compagnie_meca_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_meca_Ennemi.png'),
            "Compagnie_moto_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_moto_Ennemi.png'),
            "Groupement_meca_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_meca_Ennemi.png'),
            "Groupement_moto_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_moto_Ennemi.png'),
            "Section_meca_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_meca_Ennemi.png'),
            "Section_moto_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_moto_Ennemi.png'),
            "Sous_groupement_meca_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous_groupement_meca_Ennemi.png'),
            
            "Sous_groupement_moto_Ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous_groupement_moto_Ennemi.png'),
            "Cas": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Cas.png'),
            "Cml": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Cml.png'),
            "Esc": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Esc.png'),
            "Geb": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Geb.png'),
            "Reco": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Reco.png'),
            "Sac": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sac.png'),
            "Jeep_ami": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Jeep_ami.png'),
            "Tank_ami": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Tank_ami.png'),
            "Jeep_ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Jeep_ennemi.png'),
            "Tank_ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Tank_ennemi.png'),
            "M113_ami": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'M113_ami.png'),
            "M113_ennemi": os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'M113_ennemi.png'),
        }
        image_path = None
        if self.object_type in image_paths:
            image_path = image_paths[self.object_type]
        elif hasattr(self, 'image'):
            image_path = self.image
        if image_path and os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            pixmap = pixmap.scaled(self.icon_size, self.icon_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            # Center the pixmap at (0,0)
            self.graphic = MovablePixmapItem(self, pixmap)
            self.graphic.setOffset(-pixmap.width() / 2, -pixmap.height() / 2)
        else:
            half = self.icon_size / 2
            self.graphic = MovableRectItem(self, -half, -half, self.icon_size, self.icon_size)
            self.graphic.setBrush(QBrush(self.color))
            self.graphic.setPen(QPen(QColor("black")))
        self.graphic.setPos(position[0], position[1])
        # Ensure military units are above LOS graphics for selection
        self.graphic.setZValue(20)  # Higher than LOS graphics (which are at 5)
        self.scene.addItem(self.graphic)

        # Print initial coordinates after adding to scene
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, "get_xyz_from_scene_point"):
                from PyQt5.QtCore import QPointF
                pos = QPointF(position[0], position[1])
                xyz = main_win.get_xyz_from_scene_point(pos)
                # print(f"[DEBUG] (Created) {self.name}: {xyz}")

    def create_label(self, position):
        self.label = QGraphicsTextItem(str(self.designation))
        self.label.setDefaultTextColor(QColor("red"))
        self.update_label_position()
        self.scene.addItem(self.label)

    def set_label_text(self, text):
        if hasattr(self, 'label'):
            # print(f"[DEBUG] set_label_text called with: {text}")
            self.label.setPlainText(str(text))
            self.label.setDefaultTextColor(QColor("black"))
            font = self.label.font()
            font.setPointSize(16)
            self.label.setFont(font)

    def update_label_position(self):
        if hasattr(self, 'graphic'):
            # Place label to the left of the graphic, vertically centered
            x = self.graphic.x() - 40  # 40 px to the left
            y = self.graphic.y() - 10  # adjust as needed for centering
            self.label.setPos(x, y)

    def draw_los_polygon(self):
        self.los_update_counter += 1
        if self.los_update_counter < 10:
            if self.los_update_counter == 1:
                # print(f"[DEBUG][LOS] Skipping AP LOS calculation for {self.name} (throttled)")
                pass
            return
        # print(f"[DEBUG][LOS] Calculating AP LOS for {self.name} (every 10th call)")
        self.los_update_counter = 0
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
        # Always (re)create the polygon for simulation
        if hasattr(self, 'los_polygon_item') and self.los_polygon_item is not None:
            self.los_polygon_item.setParentItem(None)
            if self.los_polygon_item.scene() is not None:
                self.scene.removeItem(self.los_polygon_item)
            self.los_polygon_item = None
        poly = self.compute_los_polygon()
        if poly and not poly.isEmpty():
            local_points = []
            for pt in poly:
                local_pt = self.graphic.mapFromScene(pt)
                local_points.append(local_pt)
            local_poly = QPolygonF(local_points)
            radius_px = self.ap_radius_px if hasattr(self, 'ap_radius_px') else 100
            self.los_polygon_item = AnimatedRadarLOS(
                local_poly, radius_px, self.graphic,
                pen_color=QColor(225, 119, 38, 220), brush_color=QColor(225, 119, 38, 100)
            )
            self.los_polygon_item.setOpacity(1.0 if self.portee1_visible else 0.0)
        # Call update_simulation after LOS update
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, 'update_simulation'):
                main_win.update_simulation()

    def take_damage(self, amount):
        pass  # No physics/health logic

    def remove(self):
        self.scene.removeItem(self.graphic)
        if hasattr(self, 'label'):
            self.scene.removeItem(self.label)
        if hasattr(self, 'portee2_los_polygon_item') and self.portee2_los_polygon_item is not None:
            if self.portee2_los_polygon_item.scene() is not None:
                self.scene.removeItem(self.portee2_los_polygon_item)
            self.portee2_los_polygon_item = None
        if hasattr(self, 'portee3_los_polygon_item') and self.portee3_los_polygon_item is not None:
            if self.portee3_los_polygon_item.scene() is not None:
                self.scene.removeItem(self.portee3_los_polygon_item)
            self.portee3_los_polygon_item = None

    def set_ap_radius_by_meters(self, meters):
        # Try to get pixel-per-meter from the main window's GeoTIFF info
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, 'geotiff_ds') and main_win.geotiff_ds:
                pixel_width = main_win.geotiff_ds.RasterXSize
                pixel_height = main_win.geotiff_ds.RasterYSize
                gt = main_win.geotiff_geotransform
                if gt:
                    lon1, lat1 = main_win.tiff_pixel_to_geo(0, pixel_height // 2)
                    lon2, lat2 = main_win.tiff_pixel_to_geo(pixel_width - 1, pixel_height // 2)
                    map_width_m = main_win.haversine(lon1, lat1, lon2, lat2)
                    map_width_px = pixel_width
                    px_per_meter = map_width_px / map_width_m if map_width_m else 1
                    radius_px = meters * px_per_meter
                    # print(f"[DEBUG] Full map width: {map_width_m:.2f} m, {map_width_px} px, px_per_meter={px_per_meter:.4f}")
                    # print(f"[DEBUG] Set AP radius: {meters}m = {radius_px:.2f}px")
                    self.ap_radius_px = radius_px
                    self.draw_los_polygon()
                    return
        # Fallback: use default
        self.ap_radius_px = 100
        self.draw_los_polygon()
        # print("[DEBUG] Could not determine pixel-per-meter, using default radius.")

    def compute_los_polygon(self, num_rays=90 , step_m=50, observer_height=5.0):
        """
        Compute the visible area (LOS) as a polygon using the GeoTIFF elevation and a 2m observer height.
        Returns a QPolygonF in scene coordinates.
        """
        scene = self.scene
        if not (scene and scene.views()):
            return None
        view = scene.views()[0]
        main_win = view.parentWidget().window()
        if not (hasattr(main_win, 'geotiff_ds') and main_win.geotiff_ds):
            return None
        # Get observer position and elevation
        pos = self.graphic.pos()
        xyz = main_win.get_xyz_from_scene_point(pos)
        if not xyz:
            return None
        lon0, lat0, z0 = xyz
        observer_z = z0 + observer_height
        radius_m = self.ap_radius_m
        points = []
        import math
        for i in range(num_rays):
            angle = 2 * math.pi * i / num_rays
            blocked = False
            last_visible = None  # Fix: initialize before inner loop
            for d in range(0, int(radius_m)+1, step_m):
                dx = d * math.cos(angle)
                dy = d * math.sin(angle)
                dlat = (dy / 111320)
                dlon = (dx / (111320 * math.cos(math.radians(lat0))))
                lon = lon0 + dlon
                lat = lat0 + dlat
                scene_x, scene_y = main_win.geo_to_scene(lon, lat)
                xyz_target = main_win.get_xyz_from_scene_point(QPointF(scene_x, scene_y))
                if not xyz_target:
                    continue
                _, _, z_target = xyz_target
                if z_target > observer_z:
                    blocked = True
                    break
                last_visible = QPointF(scene_x, scene_y)
            if last_visible is not None:
                points.append(last_visible)
        return QPolygonF(points)

    def set_ac_radius_by_meters(self, meters):
        # Try to get pixel-per-meter from the main window's GeoTIFF info
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, 'geotiff_ds') and main_win.geotiff_ds:
                pixel_width = main_win.geotiff_ds.RasterXSize
                pixel_height = main_win.geotiff_ds.RasterYSize
                gt = main_win.geotiff_geotransform
                if gt:
                    lon1, lat1 = main_win.tiff_pixel_to_geo(0, pixel_height // 2)
                    lon2, lat2 = main_win.tiff_pixel_to_geo(pixel_width - 1, pixel_height // 2)
                    map_width_m = main_win.haversine(lon1, lat1, lon2, lat2)
                    map_width_px = pixel_width
                    px_per_meter = map_width_px / map_width_m if map_width_m else 1
                    radius_px = meters * px_per_meter
                    # print(f"[DEBUG] (Portee2) Full map width: {map_width_m:.2f} m, {map_width_px} px, px_per_meter={px_per_meter:.4f}")
                    # print(f"[DEBUG] Set Portee2 radius: {meters}m = {radius_px:.2f}px")
                    self.ac_radius_px = radius_px
                    self.draw_portee2_los_polygon()
                    return
        # Fallback: use default
        self.ac_radius_px = 400
        self.draw_portee2_los_polygon()
        # print("[DEBUG] Could not determine pixel-per-meter for portee2, using default radius.")

    def draw_portee2_los_polygon(self):
        self.portee2_los_update_counter += 1
        if self.portee2_los_update_counter < 10:
            if self.portee2_los_update_counter == 1:
                pass
                # print(f"[DEBUG][LOS] Skipping AC LOS calculation for {self.name} (throttled)")
            return
        # print(f"[DEBUG][LOS] Calculating AC LOS for {self.name} (every 10th call)")
        self.portee2_los_update_counter = 0
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
        # Always (re)create the polygon for simulation
        if hasattr(self, 'portee2_los_polygon_item') and self.portee2_los_polygon_item is not None:
            self.portee2_los_polygon_item.setParentItem(None)
            if self.portee2_los_polygon_item.scene() is not None:
                self.scene.removeItem(self.portee2_los_polygon_item)
            self.portee2_los_polygon_item = None
        poly = self.compute_portee2_los_polygon()
        if poly and not poly.isEmpty():
            local_points = []
            for pt in poly:
                local_pt = self.graphic.mapFromScene(pt)
                local_points.append(local_pt)
            local_poly = QPolygonF(local_points)
            radius_px = self.ac_radius_px if hasattr(self, 'ac_radius_px') else 100
            self.portee2_los_polygon_item = AnimatedRadarLOS(
                local_poly, radius_px, self.graphic,
                pen_color=QColor(151, 208, 56, 180), brush_color=QColor(151, 208, 56, 60)
            )
            self.portee2_los_polygon_item.setZValue(self.graphic.zValue() + 1)
            self.portee2_los_polygon_item.setOpacity(1.0 if self.portee2_visible else 0.0)
        # Call update_simulation after LOS update
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, 'update_simulation'):
                main_win.update_simulation()

    def compute_portee2_los_polygon(self, num_rays=90, step_m=50, observer_height=5.0):
        scene = self.scene
        if not (scene and scene.views()):
            return None
        view = scene.views()[0]
        main_win = view.parentWidget().window()
        if not (hasattr(main_win, 'geotiff_ds') and main_win.geotiff_ds):
            return None
        pos = self.graphic.pos()
        xyz = main_win.get_xyz_from_scene_point(pos)
        if not xyz:
            return None
        lon0, lat0, z0 = xyz
        observer_z = z0 + observer_height
        radius_m = self.ac_radius_m
        points = []
        import math
        for i in range(num_rays):
            angle = 2 * math.pi * i / num_rays
            last_visible = None
            for d in range(0, int(radius_m)+1, step_m):
                dx = d * math.cos(angle)
                dy = d * math.sin(angle)
                dlat = (dy / 111320)
                dlon = (dx / (111320 * math.cos(math.radians(lat0))))
                lon = lon0 + dlon
                lat = lat0 + dlat
                scene_x, scene_y = main_win.geo_to_scene(lon, lat)
                xyz_target = main_win.get_xyz_from_scene_point(QPointF(scene_x, scene_y))
                if not xyz_target:
                    continue
                _, _, z_target = xyz_target
                if z_target > observer_z:
                    break
                last_visible = QPointF(scene_x, scene_y)
            if last_visible is not None:
                points.append(last_visible)
        return QPolygonF(points)

    def set_missile_radius_by_meters(self, meters):
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, 'geotiff_ds') and main_win.geotiff_ds:
                pixel_width = main_win.geotiff_ds.RasterXSize
                pixel_height = main_win.geotiff_ds.RasterYSize
                gt = main_win.geotiff_geotransform
                if gt:
                    lon1, lat1 = main_win.tiff_pixel_to_geo(0, pixel_height // 2)
                    lon2, lat2 = main_win.tiff_pixel_to_geo(pixel_width - 1, pixel_height // 2)
                    map_width_m = main_win.haversine(lon1, lat1, lon2, lat2)
                    map_width_px = pixel_width
                    px_per_meter = map_width_px / map_width_m if map_width_m else 1
                    radius_px = meters * px_per_meter
                    # print(f"[DEBUG] (Portee3) Full map width: {map_width_m:.2f} m, {map_width_px} px, px_per_meter={px_per_meter:.4f}")
                    # print(f"[DEBUG] Set Portee3 radius: {meters}m = {radius_px:.2f}px")
                    self.missile_radius_px = radius_px
                    self.draw_portee3_los_polygon()
                    return
        self.missile_radius_px = 100
        self.draw_portee3_los_polygon()
        # print("[DEBUG] Could not determine pixel-per-meter for portee3, using default radius.")

    def draw_portee3_los_polygon(self):
        self.portee3_los_update_counter += 1
        if self.portee3_los_update_counter < 10:
            if self.portee3_los_update_counter == 1:
                pass    
                # print(f"[DEBUG][LOS] Skipping Missile LOS calculation for {self.name} (throttled)")
            return
        # print(f"[DEBUG][LOS] Calculating Missile LOS for {self.name} (every 10th call)")
        self.portee3_los_update_counter = 0
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
        # Always (re)create the polygon for simulation
        if hasattr(self, 'portee3_los_polygon_item') and self.portee3_los_polygon_item is not None:
            self.portee3_los_polygon_item.setParentItem(None)
            if self.portee3_los_polygon_item.scene() is not None:
                self.scene.removeItem(self.portee3_los_polygon_item)
            self.portee3_los_polygon_item = None
        poly = self.compute_portee3_los_polygon()
        if poly and not poly.isEmpty():
            local_points = []
            for pt in poly:
                local_pt = self.graphic.mapFromScene(pt)
                local_points.append(local_pt)
            local_poly = QPolygonF(local_points)
            radius_px = self.missile_radius_px if hasattr(self, 'missile_radius_px') else 100
            self.portee3_los_polygon_item = AnimatedRadarLOS(
                local_poly, radius_px, self.graphic,
                pen_color=QColor(0, 91, 91, 120), brush_color=QColor(0, 91, 91, 50)
            )
            self.portee3_los_polygon_item.setZValue(self.graphic.zValue() + 2)
            self.portee3_los_polygon_item.setOpacity(1.0 if self.portee3_visible else 0.0)
        # Call update_simulation after LOS update
        scene = self.scene
        if scene and scene.views():
            view = scene.views()[0]
            main_win = view.parentWidget().window()
            if hasattr(main_win, 'update_simulation'):
                main_win.update_simulation()

    def compute_portee3_los_polygon(self, num_rays=90, step_m=50, observer_height=5.0):
        scene = self.scene
        if not (scene and scene.views()):
            return None
        view = scene.views()[0]
        main_win = view.parentWidget().window()
        if not (hasattr(main_win, 'geotiff_ds') and main_win.geotiff_ds):
            return None
        pos = self.graphic.pos()
        xyz = main_win.get_xyz_from_scene_point(pos)
        if not xyz:
            return None
        lon0, lat0, z0 = xyz
        observer_z = z0 + observer_height
        radius_m = self.missile_radius_m
        points = []
        import math
        for i in range(num_rays):
            angle = 2 * math.pi * i / num_rays
            last_visible = None
            for d in range(0, int(radius_m)+1, step_m):
                dx = d * math.cos(angle)
                dy = d * math.sin(angle)
                dlat = (dy / 111320)
                dlon = (dx / (111320 * math.cos(math.radians(lat0))))
                lon = lon0 + dlon
                lat = lat0 + dlat
                scene_x, scene_y = main_win.geo_to_scene(lon, lat)
                xyz_target = main_win.get_xyz_from_scene_point(QPointF(scene_x, scene_y))
                if not xyz_target:
                    continue
                _, _, z_target = xyz_target
                if z_target > observer_z:
                    break
                last_visible = QPointF(scene_x, scene_y)
            if last_visible is not None:
                points.append(last_visible)
        return QPolygonF(points)

    def set_portee_visible(self, portee_num, visible):
        # Only set opacity, never block polygon creation/update
        opacity = 1.0 if visible else 0.2  # 20% opacity for debug
        if portee_num == 1:
            self.portee1_visible = visible
            self.draw_los_polygon()
            if hasattr(self, 'los_polygon_item') and self.los_polygon_item is not None:
                self.los_polygon_item.setOpacity(opacity)
        elif portee_num == 2:
            self.portee2_visible = visible
            self.draw_portee2_los_polygon()
            if hasattr(self, 'portee2_los_polygon_item') and self.portee2_los_polygon_item is not None:
                self.portee2_los_polygon_item.setOpacity(opacity)
        elif portee_num == 3:
            self.portee3_visible = visible
            self.draw_portee3_los_polygon()
            if hasattr(self, 'portee3_los_polygon_item') and self.portee3_los_polygon_item is not None:
                self.portee3_los_polygon_item.setOpacity(opacity)

    def show_all_portee(self):
        self.show_portee1()
        self.show_portee2()
        self.show_portee3()

    def hide_all_portee(self):
        self.hide_portee1()
        self.hide_portee2()
        self.hide_portee3()
 
    def show_portee1(self):
        self.portee1_visible = True
        self.draw_los_polygon()
        if self.los_polygon_item:
            self.los_polygon_item.setOpacity(1.0)

    def hide_portee1(self):
        self.portee1_visible = False
        if hasattr(self, 'los_polygon_item') and self.los_polygon_item is not None:
            self.los_polygon_item.setOpacity(0.0)

    def show_portee2(self):
        self.portee2_visible = True
        self.draw_portee2_los_polygon()
        if self.portee2_los_polygon_item:
            self.portee2_los_polygon_item.setOpacity(1.0)

    def hide_portee2(self):
        self.portee2_visible = False
        if hasattr(self, 'portee2_los_polygon_item') and self.portee2_los_polygon_item is not None:
            self.portee2_los_polygon_item.setOpacity(0.0)

    def show_portee3(self):
        self.portee3_visible = True
        self.draw_portee3_los_polygon()
        if self.portee3_los_polygon_item:
            self.portee3_los_polygon_item.setOpacity(1.0)

    def hide_portee3(self):
        self.portee3_visible = False
        if hasattr(self, 'portee3_los_polygon_item') and self.portee3_los_polygon_item is not None:
            self.portee3_los_polygon_item.setOpacity(0.0)

    def create_health_bar(self):
        if self.health_bar_item:
            self.health_bar_item.setParentItem(None)
            self.scene.removeItem(self.health_bar_item)
        from PyQt5.QtWidgets import QGraphicsRectItem
        from PyQt5.QtGui import QBrush, QColor
        bar_width = self.icon_size
        bar_height = 2  # Thinner bar
        # Position relative to the graphic (centered below)
        x = -bar_width / 2
        y = self.icon_size / 2 - 2# Minimal gap
        self.health_bar_item = QGraphicsRectItem(x, y, bar_width, bar_height, self.graphic)
        self.health_bar_item.setBrush(QBrush(QColor(39, 174, 96)))
        self.health_bar_item.setPen(QColor(44, 62, 80))
        self.health_bar_item.setZValue(self.graphic.zValue() + 1)
        self.update_health_bar()
        self.health_bar_item.setVisible(self.health_bar_visible)

    def update_health_bar(self):
        current = self.total_health()
        max_health = getattr(self, 'init_health', current)
        # Update live palette health bar if present
        if hasattr(self, 'health_bar'):
            self.health_bar.setMaximum(int(max_health))
            self.health_bar.setValue(int(current))
        if hasattr(self, 'health_label'):
            self.health_label.setText(f"Health: {current:.1f}/{max_health:.1f}")
        # --- Update on-map health bar (QGraphicsRectItem) ---
        if self.health_bar_item:
            percent = current / max_health if max_health > 0 else 0
            bar_width = self.icon_size
            bar_height = 2
            # Set width proportional to health
            self.health_bar_item.setRect(-bar_width / 2, self.icon_size / 2 - 2, bar_width * percent, bar_height)
            # Color transition: green→yellow→red
            if percent > 0.6:
                color = QColor(39, 174, 96)  # Green
            elif percent > 0.3:
                color = QColor(241, 196, 15)  # Yellow
            else:
                color = QColor(231, 76, 60)  # Red
            self.health_bar_item.setBrush(QBrush(color))

    def set_health_bar_visible(self, visible):
        self.health_bar_visible = visible
        if self.health_bar_item:
            self.health_bar_item.setVisible(visible)

    def update_graphics(self):
        # No need to update health bar position; it is now a child of the graphic item
        pass

    def get_portee_polygon_scene(self, portee_type):
        """
        Returns the portée polygon (AP, AC, Missile) in scene coordinates for overlap detection.
        """
        if portee_type == 'AP' and self.los_polygon_item:
            print(f"[DEBUG][POLY] {self.name} AP polygon exists: {self.los_polygon_item is not None}")
            return QPolygonF([self.graphic.mapToScene(pt) for pt in self.los_polygon_item.polygon()])
        elif portee_type == 'AC' and self.portee2_los_polygon_item:
            print(f"[DEBUG][POLY] {self.name} AC polygon exists: {self.portee2_los_polygon_item is not None}")
            return QPolygonF([self.graphic.mapToScene(pt) for pt in self.portee2_los_polygon_item.polygon()])
        elif portee_type == 'Missile' and self.portee3_los_polygon_item:
            print(f"[DEBUG][POLY] {self.name} Missile polygon exists: {self.portee3_los_polygon_item is not None}")
            return QPolygonF([self.graphic.mapToScene(pt) for pt in self.portee3_los_polygon_item.polygon()])
        print(f"[DEBUG][POLY] {self.name} {portee_type} polygon: None")
        return None

    def sync_simulation_update_and_overlap(self, portee_type):
        def get_side(obj):
            return 'ennemi' if 'ennemi' in obj.name.lower() else 'ami'

        scene = self.scene
        if not (scene and scene.views()):
            print(f"[DEBUG][OVERLAP] {self.name} {portee_type}: scene/views missing")
            return []
        view = scene.views()[0]
        main_win = view.parentWidget().window()
        results = []
        my_side = get_side(self)
        if hasattr(main_win, 'live_objects'):
            print(f"[DEBUG][LIVE_OBJECTS] {self.name}: {[o.name for o in main_win.live_objects]}")
            my_poly_scene = self.get_portee_polygon_scene(portee_type)
            if my_poly_scene is None:
                print(f"[DEBUG][OVERLAP] {self.name} {portee_type}: my_poly_scene is None")
                return []
            for other in main_win.live_objects:
                if other is self:
                    continue
                other_side = get_side(other)
                if my_side == other_side:
                    continue  # Skip friendly fire
                if hasattr(other, 'graphic'):
                    other_br = other.graphic.boundingRect()
                    scene_corners = [
                        other.graphic.mapToScene(other_br.topLeft()),
                        other.graphic.mapToScene(other_br.topRight()),
                        other.graphic.mapToScene(other_br.bottomRight()),
                        other.graphic.mapToScene(other_br.bottomLeft())
                    ]
                    rect_poly_scene = QPolygonF(scene_corners)
                    if my_poly_scene.intersects(rect_poly_scene):
                        print(f"[DEBUG][OVERLAP] {self.name} {portee_type}: INTERSECTS with {other.name}")
                        results.append((other, portee_type, 'rect'))
        else:
            print(f"[DEBUG][LIVE_OBJECTS] {self.name}: main_win has no live_objects")
        print(f"[DEBUG][OVERLAP] {self.name} {portee_type}: {[o[0].name for o in results]}")
        return results

    def distribute_incoming_damage(self, attackers, weapon_type):
        if not attackers:
            return
        n_attackers = len(attackers)
        if weapon_type == 'AP':
            total_firepower = sum(getattr(a, 'ap_count', 0) for a in attackers)
            per_attacker = total_firepower // n_attackers if n_attackers > 0 else 0
            self.apply_damage({'personnel': per_attacker, 'vehicle': per_attacker})
        elif weapon_type == 'AC':
            total_firepower = sum(getattr(a, 'ac_count', 0) for a in attackers)
            per_attacker = total_firepower // n_attackers if n_attackers > 0 else 0
            self.apply_damage({'mechanized': per_attacker, 'tank': per_attacker})
        elif weapon_type == 'Missile':
            total_firepower = sum(getattr(a, 'missile_count', 0) for a in attackers)
            per_attacker = total_firepower // n_attackers if n_attackers > 0 else 0
            self.apply_damage({'tank': per_attacker, 'mechanized': per_attacker, 'vehicle': per_attacker})

    def show_firing_effect(self, target, weapon_type):
        if not hasattr(self, 'scene') or not hasattr(target, 'graphic') or not hasattr(self, 'graphic'):
            return
            
        # Define weapon-specific visual properties
        weapon_properties = {
            'AP': {
                'color': QColor(255, 255, 0, 180),  # Yellow with alpha
                'width': 1.5,
                'duration': 200,
                'fade_steps': 20,
                'particle_count': 4,
                'particle_size': 4,
                'dash_pattern': [2, 2]
            },
            'AC': {
                'color': QColor(255, 165, 0, 160),  # Orange with alpha
                'width': 2,
                'duration': 250,
                'fade_steps': 25,
                'particle_count': 6,
                'particle_size': 5,
                'dash_pattern': [3, 2]
            },
            'Missile': {
                'color': QColor(255, 0, 0, 140),    # Red with alpha
                'width': 2.5,
                'duration': 300,
                'fade_steps': 30,
                'particle_count': 8,
                'particle_size': 6,
                'dash_pattern': [4, 2]
            }
        }
        
        props = weapon_properties.get(weapon_type, weapon_properties['AP'])
        
        # Get start and end positions
        start = self.graphic.pos()
        end = target.graphic.pos()
        
        # Create main firing line with fade effect
        line = QGraphicsLineItem(start.x(), start.y(), end.x(), end.y())
        pen = QPen(props['color'], props['width'])
        pen.setStyle(Qt.CustomDashLine)
        pen.setDashPattern(props['dash_pattern'])
        line.setPen(pen)
        line.setZValue(100)
        line.setOpacity(0)  # Start fully transparent
        self.scene.addItem(line)
        
        # Fade in the line
        for step in range(props['fade_steps'] // 2):
            QTimer.singleShot(
                step * (props['duration'] // props['fade_steps']),
                lambda s=step, t=props['fade_steps']//2: self._fade_line(line, s, t, fade_in=True)
            )
        
        # Fade out the line
        for step in range(props['fade_steps'] // 2, props['fade_steps']):
            QTimer.singleShot(
                step * (props['duration'] // props['fade_steps']),
                lambda s=step, t=props['fade_steps']//2: self._fade_line(line, s, t, fade_in=False)
            )
        
        # Create particles along the line
        for i in range(props['particle_count']):
            # Calculate particle position along the line
            t = (i + 1) / (props['particle_count'] + 1)
            x = start.x() + (end.x() - start.x()) * t
            y = start.y() + (end.y() - start.y()) * t
            
            # Create particle
            size = props['particle_size']
            particle = QGraphicsEllipseItem(-size/2, -size/2, size, size)
            particle.setPos(x, y)
            particle.setBrush(props['color'])
            no_pen = QPen(Qt.NoPen)
            particle.setPen(no_pen)
            particle.setZValue(101)
            particle.setOpacity(0)  # Start fully transparent
            self.scene.addItem(particle)
            
            # Fade in the particle
            for step in range(props['fade_steps'] // 2):
                QTimer.singleShot(
                    step * (props['duration'] // props['fade_steps']),
                    lambda p=particle, s=step, t=props['fade_steps']//2: self._fade_particle(p, s, t, fade_in=True)
                )
            
            # Fade out the particle
            for step in range(props['fade_steps'] // 2, props['fade_steps']):
                QTimer.singleShot(
                    step * (props['duration'] // props['fade_steps']),
                    lambda p=particle, s=step, t=props['fade_steps']//2: self._fade_particle(p, s, t, fade_in=False)
                )
        
        # Remove main line after duration
        QTimer.singleShot(props['duration'], lambda: self.scene.removeItem(line))
        
    def _fade_line(self, line, step, total_steps, fade_in=True):
        """Helper method to fade a line in or out"""
        if line.scene():
            if fade_in:
                opacity = step / total_steps
            else:
                opacity = 1.0 - (step / total_steps)
            line.setOpacity(opacity)
            if not fade_in and step == total_steps - 1:
                line.scene().removeItem(line)
                
    def _fade_particle(self, particle, step, total_steps, fade_in=True):
        """Helper method to fade a particle in or out"""
        if particle.scene():
            if fade_in:
                opacity = step / total_steps
            else:
                opacity = 1.0 - (step / total_steps)
            particle.setOpacity(opacity)
            if not fade_in and step == total_steps - 1:
                particle.scene().removeItem(particle)

    def play_firing_sound(self, weapon_type):
        import os
        from PyQt5.QtMultimedia import QSoundEffect
        from PyQt5.QtCore import QUrl, QTimer
        
        # Only use AP sound as background
        sound_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'sounds', 'ap.wav')
        print(f"[DEBUG][AUDIO] Attempting to play sound from: {sound_path}")
        
        if not os.path.exists(sound_path):
            print(f"[DEBUG][AUDIO] Sound file does not exist: {sound_path}")
            return
            
        try:
            # Initialize sound effect if it doesn't exist
            if not hasattr(self, '_bg_sound'):
                print("[DEBUG][AUDIO] Creating new sound effect")
                self._bg_sound = QSoundEffect()
                self._bg_sound.setSource(QUrl.fromLocalFile(sound_path))
                self._bg_sound.setVolume(0.3)
                self._bg_sound.setLoopCount(QSoundEffect.Infinite)
                print(f"[DEBUG][AUDIO] Sound effect created with volume: {self._bg_sound.volume()}")
            
            # Start sound if not playing
            if not self._bg_sound.isPlaying():
                print("[DEBUG][AUDIO] Starting background sound (conflict detected)")
                self._bg_sound.play()
                print(f"[DEBUG][AUDIO] Sound status - Playing: {self._bg_sound.isPlaying()}, Status: {self._bg_sound.status()}")
            
            # Reset the stop timer
            if hasattr(self, '_sound_timer'):
                self._sound_timer.stop()
            
            # Create new timer to stop sound
            self._sound_timer = QTimer()
            self._sound_timer.setSingleShot(True)
            self._sound_timer.timeout.connect(self._stop_bg_sound)
            self._sound_timer.start(1000)  # Check for conflict every second
            
        except Exception as e:
            print(f"[DEBUG][AUDIO] Exception in play_firing_sound: {e}")

    def _stop_bg_sound(self):
        """Stop the background sound if no conflict"""
        print("[DEBUG][AUDIO] Checking if sound should stop")
        if hasattr(self, '_bg_sound') and self._bg_sound.isPlaying():
            # Check if there's still conflict
            if not self._check_for_conflict():
                print("[DEBUG][AUDIO] No conflict detected, stopping sound")
                self._bg_sound.stop()
                print("[DEBUG][AUDIO] Stopped background sound (no conflict)")
            else:
                print("[DEBUG][AUDIO] Conflict still exists, continuing sound")
                # If there's still conflict, restart the timer
                self._sound_timer.start(1000)
                
    def _check_for_conflict(self):
        """Check if there's any active conflict/firing"""
        # Check if this unit has any valid targets in range
        for portee_type in ['AP', 'AC', 'Missile']:
            overlaps = self.sync_simulation_update_and_overlap(portee_type)
            if overlaps:  # If we have any targets in range, there's a conflict
                print(f"[DEBUG][AUDIO] Conflict detected with {portee_type} weapon")
                return True
        print("[DEBUG][AUDIO] No conflict detected")
        return False

    def attack_targets(self, targets, weapon_type):
        print(f"[DEBUG][FIRE] {self.name} attempting to fire {weapon_type} at {[t.name for t in targets]}")
        WEAPON_DAMAGE = {'AP': 0.1, 'AC': 0.2, 'Missile': 0.5}
        if not targets:
            return
        self.is_firing = True
        damage = WEAPON_DAMAGE.get(weapon_type, 0)
        for target in targets:
            self.show_firing_effect(target, weapon_type)
            self.play_firing_sound(weapon_type)
            health_before = target.total_health() if hasattr(target, 'total_health') else None
            if weapon_type == 'AP':
                target.apply_damage({'personnel': damage, 'vehicle': damage})
            elif weapon_type == 'AC':
                target.apply_damage({'mechanized': damage, 'tank': damage})
            elif weapon_type == 'Missile':
                target.apply_damage({'tank': damage, 'mechanized': damage, 'vehicle': damage})
            health_after = target.total_health() if hasattr(target, 'total_health') else None
            # Log damage event
            main_win = None
            if hasattr(self, 'scene') and self.scene and self.scene.views():
                view = self.scene.views()[0]
                if hasattr(view, 'parentWidget') and view.parentWidget():
                    main_win = view.parentWidget().window()
            if main_win and hasattr(main_win, 'damage_log'):
                import time
                main_win.damage_log.append({
                    'time': getattr(main_win, 'current_scene_index', None),
                    'timestamp': time.time(),
                    'attacker': self.name,
                    'target': target.name,
                    'weapon': weapon_type,
                    'damage': damage,
                    'health_before': health_before,
                    'health_after': health_after
                })
        QTimer.singleShot(1000, lambda: setattr(self, 'is_firing', False))

    def apply_damage(self, damage_dict):
        print(f"[DEBUG][DAMAGE] {self.name} apply_damage: {damage_dict}")
        self.personnel_count = max(0, self.personnel_count - damage_dict.get('personnel', 0))
        self.vehicle_count = max(0, self.vehicle_count - damage_dict.get('vehicle', 0))
        self.mechanized_count = max(0, self.mechanized_count - damage_dict.get('mechanized', 0))
        self.tank_count = max(0, self.tank_count - damage_dict.get('tank', 0))
        self.update_health_bar()
        print(f"[DEBUG][HEALTH] {self.name} health after damage: {self.total_health()}")

    def total_health(self):
        """
        Returns the current total health of the object based on its unit counts.
        """
        return (
            max(0, getattr(self, 'personnel_count', 0)) * 1 +
            max(0, getattr(self, 'vehicle_count', 0)) * 15 +
            max(0, getattr(self, 'mechanized_count', 0)) * 30 +
            max(0, getattr(self, 'tank_count', 0)) * 60
        )

    def set_personnel_count(self, value):
        self.personnel_count = value
        self.update_health_bar()
    def set_vehicle_count(self, value):
        self.vehicle_count = value
        self.update_health_bar()
    def set_mechanized_count(self, value):
        self.mechanized_count = value
        self.update_health_bar()
    def set_tank_count(self, value):
        self.tank_count = value
        self.update_health_bar()

    def reset_to_initial_state(self):
        self.personnel_count = self._initial_personnel_count
        self.vehicle_count = self._initial_vehicle_count
        self.mechanized_count = self._initial_mechanized_count
        self.tank_count = self._initial_tank_count
        # Update QLineEdit fields in live objects panel if they exist
        if hasattr(self, 'personnel_count_edit'):
            self.personnel_count_edit.setText(str(self.personnel_count))
        if hasattr(self, 'vehicle_count_edit'):
            self.vehicle_count_edit.setText(str(self.vehicle_count))
        if hasattr(self, 'mechanized_count_edit'):
            self.mechanized_count_edit.setText(str(self.mechanized_count))
        if hasattr(self, 'tank_count_edit'):
            self.tank_count_edit.setText(str(self.tank_count))
        self.update_health_bar()

class S_gpt_meca:
    def __init__(self):
        self.name = "S_gpt_meca"
        self.designation = "Support GPT Mechanised"
        self.color = "#4A90E2"  # blue
        self.hierarchy = 1  # Brigade
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 400
        self.ac_radius_m = 800
        self.missile_radius_m = 1600
        self.ap_count = 100
        self.ac_count = 50
        self.missile_count = 10
        self.speed = 10  # m/s (example for mechanized support)

class S_gpt_moto:
    def __init__(self):
        self.name = "S_gpt_moto"
        self.designation = "Support GPT Motorised"
        self.color = "#50E3C2"  # teal
        self.hierarchy = 2  # Groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 350
        self.ac_radius_m = 700
        self.missile_radius_m = 1400
        self.ap_count = 80
        self.ac_count = 40
        self.missile_count = 8
        self.speed = 8  # m/s (example for motorized support)

class gpt_meca:
    def __init__(self):
        self.name = "gpt_meca"
        self.designation = "GPT Mechanised"
        self.color = "#F5A623"  # orange
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 300
        self.ac_radius_m = 600
        self.missile_radius_m = 1200
        self.ap_count = 60
        self.ac_count = 30
        self.missile_count = 6
        self.speed = 10  # m/s

class gpt_moto:
    def __init__(self):
        self.name = "gpt_moto"
        self.designation = "GPT Motorised"
        self.color = "#B8E986"  # green
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 250
        self.ac_radius_m = 500
        self.missile_radius_m = 1000
        self.ap_count = 40
        self.ac_count = 20
        self.missile_count = 4
        self.speed = 8  # m/s

# --- Ennemi unit classes ---
class enmi_brigade:
    def __init__(self):
        self.name = "enmi_brigade"
        self.designation = "Ennemi Brigade"
        self.color = "#E74C3C"  # red
        self.hierarchy = 1  # Brigade
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 420
        self.ac_radius_m = 820
        self.missile_radius_m = 1620
        self.ap_count = 90
        self.ac_count = 45
        self.missile_count = 9
        self.speed = 10  # m/s

class enmi_company:
    def __init__(self):
        self.name = "enmi_company"
        self.designation = "Ennemi Company"
        self.color = "#C0392B"  # dark red
        self.hierarchy = 2  # Groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 320
        self.ac_radius_m = 620
        self.missile_radius_m = 1220
        self.ap_count = 70
        self.ac_count = 35
        self.missile_count = 7
        self.speed = 8  # m/s

class enmi_section:
    def __init__(self):
        self.name = "enmi_section"
        self.designation = "Ennemi Section"
        self.color = "#A93226"  # brownish red
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', self.name + '.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 5  # m/s (infantry)

class AnimatedRadarLOS(QGraphicsPolygonItem):
    def __init__(self, polygon, r, parent=None, num_rings=1, duration=5200, pen_color=QColor(0, 200, 150), brush_color=QColor(0, 200, 150, 60)):
        super().__init__(polygon, parent)
        self.r = r
        self.num_rings = num_rings
        self.duration = duration
        self.phase = 0.0
        self.pen_color = pen_color
        self.brush_color = brush_color
        # Set LOS graphics below military units for proper selection
        self.setZValue(5)  # Below military units (which are at 20)
        self.setAcceptedMouseButtons(Qt.NoButton)
        # Make sure LOS graphics don't interfere with selection
        self.setFlag(QGraphicsPolygonItem.ItemIsSelectable, False)
        self.setFlag(QGraphicsPolygonItem.ItemIsMovable, False)
        self.setPen(QPen(self.pen_color, 1))
        self.setBrush(QBrush(self.brush_color))
        self.timer = QTimer()
        self.timer.timeout.connect(self.advance_phase)
        self.timer.start(30)
        # print(f"[DEBUG] AnimatedRadarLOS created: r={r}, parent={parent}, num_rings={num_rings}, duration={duration}")

    def advance_phase(self):
        self.phase = (self.phase + 30 / self.duration) % 1.0
        self.update()

    def paint(self, painter, option, widget=None):
        painter.setRenderHint(QPainter.Antialiasing)
        # Draw semi-transparent background
        path = QPainterPath()
        path.addPolygon(self.polygon())
        painter.setPen(Qt.NoPen)
        painter.setBrush(self.brush_color)
        painter.drawPath(path)
        # Draw solid outline for the LOS polygon
        painter.setPen(QPen(self.pen_color, 1))
        painter.setBrush(Qt.NoBrush)
        painter.drawPolygon(self.polygon())
        # Draw animated rings, clipped to the polygon
        for i in range(self.num_rings):
            frac = ((i / self.num_rings) + self.phase) % 1.0
            alpha = int(180 * (1 - frac))
            color = QColor(self.pen_color)
            color.setAlpha(max(0, alpha))
            radius = self.r * frac
            ring_path = QPainterPath()
            ring_path.addEllipse(-radius, -radius, 2*radius, 2*radius)
            clipped = path.intersected(ring_path)
            painter.setPen(QPen(color, 2))
            painter.setBrush(Qt.NoBrush)
            painter.drawPath(clipped)

# --- FRIENDLY (Ami) ---
class Bataillon_meca:
    def __init__(self):
        self.name = "Bataillon_meca"
        self.designation = "Bataillon méca"
        self.color = "#0000FF"
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_meca.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12  # Mechanized infantry battalion (APC speed)

class Bataillon_moto:
    def __init__(self):
        self.name = "Bataillon_moto"
        self.designation = "Bataillon moto"
        self.color = "#0000FF"
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_moto.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8  # Motorized battalion

class Brigade_meca:
    def __init__(self):
        self.name = "Brigade_meca"
        self.designation = "Brigade méca"
        self.color = "#0000FF"
        self.hierarchy = 1  # Brigade
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_meca.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12  # Mechanized brigade

class Brigade_moto:
    def __init__(self):
        self.name = "Brigade_moto"
        self.designation = "Brigade moto"
        self.color = "#0000FF"
        self.hierarchy = 1  # Brigade
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_moto.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8  # Motorized brigade

class Compagnie_meca:
    def __init__(self):
        self.name = "Compagnie_meca"
        self.designation = "Compagnie méca"
        self.color = "#0000FF"
        self.hierarchy = 5  # Compagnie
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_meca.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12  # Mechanized company

class Compagnie_moto:
    def __init__(self):
        self.name = "Compagnie_moto"
        self.designation = "Compagnie moto"
        self.color = "#0000FF"
        self.hierarchy = 5  # Compagnie
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_moto.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8  # Motorized company

class Groupement_meca:
    def __init__(self):
        self.name = "Groupement_meca"
        self.designation = "Groupement méca"
        self.color = "#0000FF"
        self.hierarchy = 2  # Groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_meca.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12  # Mechanized group

class Groupement_moto:
    def __init__(self):
        self.name = "Groupement_moto"
        self.designation = "Groupement moto"
        self.color = "#0000FF"
        self.hierarchy = 2  # Groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_moto.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8  # Motorized group

class Section_meca:
    def __init__(self):
        self.name = "Section_meca"
        self.designation = "Section méca"
        self.color = "#0000FF"
        self.hierarchy = 6  # Section
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_meca.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 5  # Infantry section (on foot)

class Section_moto:
    def __init__(self):
        self.name = "Section_moto"
        self.designation = "Section moto"
        self.color = "#0000FF"
        self.hierarchy = 6  # Section
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_moto.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 5  # Infantry section (on foot)

class Sous_groupement_meca:
    def __init__(self):
        self.name = "Sous_groupement_meca"
        self.designation = "Sous-groupement méca"
        self.color = "#0000FF"
        self.hierarchy = 4  # Sous-groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous_groupement_meca.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 12  # Mechanized sub-group

class Sous_groupement_moto:
    def __init__(self):
        self.name = "Sous_groupement_moto"
        self.designation = "Sous-groupement moto"
        self.color = "#0000FF"
        self.hierarchy = 4  # Sous-groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous_groupement_moto.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 8  # Motorized sub-group

# --- ENEMY (Ennemi) ---
class Bataillon_meca_Ennemi:
    def __init__(self):
        self.name = "Bataillon_meca_Ennemi"
        self.designation = "Bataillon méca Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_meca_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12

class Bataillon_moto_Ennemi:
    def __init__(self):
        self.name = "Bataillon_moto_Ennemi"
        self.designation = "Bataillon moto Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Bataillon_moto_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8

class Brigade_meca_Ennemi:
    def __init__(self):
        self.name = "Brigade_meca_Ennemi"
        self.designation = "Brigade méca Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 1  # Brigade
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_meca_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12

class Brigade_moto_Ennemi:
    def __init__(self):
        self.name = "Brigade_moto_Ennemi"
        self.designation = "Brigade moto Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 1  # Brigade
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Brigade_moto_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8

class Compagnie_meca_Ennemi:
    def __init__(self):
        self.name = "Compagnie_meca_Ennemi"
        self.designation = "Compagnie méca Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 5  # Compagnie
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_meca_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12

class Compagnie_moto_Ennemi:
    def __init__(self):
        self.name = "Compagnie_moto_Ennemi"
        self.designation = "Compagnie moto Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 5  # Compagnie
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Compagnie_moto_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8

class Groupement_meca_Ennemi:
    def __init__(self):
        self.name = "Groupement_meca_Ennemi"
        self.designation = "Groupement méca Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 2  # Groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_meca_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12

class Groupement_moto_Ennemi:
    def __init__(self):
        self.name = "Groupement_moto_Ennemi"
        self.designation = "Groupement moto Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 2  # Groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Groupement_moto_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8

class Section_meca_Ennemi:
    def __init__(self):
        self.name = "Section_meca_Ennemi"
        self.designation = "Section méca Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 6  # Section
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_meca_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 5

class Section_moto_Ennemi:
    def __init__(self):
        self.name = "Section_moto_Ennemi"
        self.designation = "Section moto Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 6  # Section
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Section_moto_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 5

class Sous_groupement_meca_Ennemi:
    def __init__(self):
        self.name = "Sous_groupement_meca_Ennemi"
        self.designation = "Sous-groupement méca Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 4  # Sous-groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous-groupement_meca_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 12

class Sous_groupement_moto_Ennemi:
    def __init__(self):
        self.name = "Sous_groupement_moto_Ennemi"
        self.designation = "Sous-groupement moto Ennemi"
        self.color = "#FF0000"
        self.hierarchy = 4  # Sous-groupement
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sous-groupement_moto_Ennemi.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.personnel_count = 1500
        self.vehicle_count = 30
        self.mechanized_count = 20
        self.tank_count = 15
        self.speed = 8

class Cas:
    def __init__(self):
        self.name = "Cas"
        self.designation = "Cas"
        self.color = "#0000FF"
        self.hierarchy = 6  # section
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Cas.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 12  # Assume fast support

class Cml:
    def __init__(self):
        self.name = "Cml"
        self.designation = "Cml"
        self.color = "#0000FF"
        self.hierarchy = 5
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Cml.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 12

class Esc:
    def __init__(self):
        self.name = "Esc"
        self.designation = "Esc"
        self.color = "#0000FF"
        self.hierarchy =6
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Esc.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 16  # Recon/jeep speed

class Geb:
    def __init__(self):
        self.name = "Geb"
        self.designation = "Geb"
        self.color = "#0000FF"
        self.hierarchy = 5
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Geb.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 8  # Mountain troops, slower

class Reco:
    def __init__(self):
        self.name = "Reco"
        self.designation = "Reco"
        self.color = "#0000FF"
        self.hierarchy = 5
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Reco.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 16  # Recon/jeep speed

class Sac:
    def __init__(self):
        self.name = "Sac"
        self.designation = "Sac"
        self.color = "#0000FF"
        self.hierarchy = 6
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'Sac.png')
        self.ap_radius_m = 220
        self.ac_radius_m = 420
        self.missile_radius_m = 820
        self.ap_count = 50
        self.ac_count = 25
        self.missile_count = 5
        self.speed = 8  # Support, default

class Jeep_ami:
    def __init__(self):
        self.name = "Jeep_ami"
        self.designation = "Jeep ami"
        self.color = "#007F00"  # green
        self.hierarchy = 5  # Compagnie
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'jeep_ami.png')
        self.ap_radius_m = 800
        self.ac_radius_m = 0
        self.missile_radius_m = 0
        self.ap_count = 10
        self.ac_count = 5
        self.missile_count = 1
        self.personnel_count = 0
        self.vehicle_count = 1
        self.mechanized_count = 0
        self.tank_count = 0
        self.speed = 16  # Jeep

class Tank_ami:
    def __init__(self):
        self.name = "Tank_ami"
        self.designation = "Tank ami"
        self.color = "#003F7F"  # blue
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'tank_ami.png')
        self.ap_radius_m = 0
        self.ac_radius_m = 1000
        self.missile_radius_m = 2000
        self.ap_count = 20
        self.ac_count = 10
        self.missile_count = 2
        self.personnel_count = 0
        self.vehicle_count = 0
        self.mechanized_count = 0
        self.tank_count = 1
        self.speed = 14  # Tank

class Jeep_ennemi:
    def __init__(self):
        self.name = "Jeep_ennemi"
        self.designation = "Jeep ennemi"
        self.color = "#7F0000"  # dark red
        self.hierarchy = 5  # Compagnie
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'jeep_ennemi.png')
        self.ap_radius_m = 800
        self.ac_radius_m = 0
        self.missile_radius_m = 0
        self.ap_count = 10
        self.ac_count = 5
        self.missile_count = 1
        self.personnel_count = 0
        self.vehicle_count = 1
        self.mechanized_count = 0
        self.tank_count = 0
        self.speed = 16

class Tank_ennemi:
    def __init__(self):
        self.name = "Tank_ennemi"
        self.designation = "Tank ennemi"
        self.color = "#7F3F00"  # brown
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'tank_ennemi.png')
        self.ap_radius_m = 0
        self.ac_radius_m = 1000
        self.missile_radius_m = 2000
        self.ap_count = 20
        self.ac_count = 10
        self.missile_count = 2
        self.personnel_count = 0
        self.vehicle_count = 0
        self.mechanized_count = 0
        self.tank_count = 1
        self.speed = 14

class M113_ami:
    def __init__(self):
        self.name = "M113_ami"
        self.designation = "M113 ami"
        self.color = "#0055AA"  # blue shade
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'm113_ami.png')
        self.ap_radius_m = 1200
        self.ac_radius_m = 0
        self.missile_radius_m = 0
        self.ap_count = 12
        self.ac_count = 6
        self.missile_count = 2
        self.personnel_count = 0
        self.vehicle_count = 0
        self.mechanized_count = 1
        self.tank_count = 0
        self.speed = 12  # APC

class M113_ennemi:
    def __init__(self):
        self.name = "M113_ennemi"
        self.designation = "M113 ennemi"
        self.color = "#AA2200"  # red shade
        self.hierarchy = 3  # Bataillon
        self.icon_size = HIERARCHY_ICON_SIZES[self.hierarchy]
        self.image = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'm113_ennemi.png')
        self.ap_radius_m = 1200
        self.ac_radius_m = 0
        self.missile_radius_m = 0
        self.ap_count = 12
        self.ac_count = 6
        self.missile_count = 2
        self.personnel_count = 0
        self.vehicle_count = 0
        self.mechanized_count = 1
        self.tank_count = 0
        self.speed = 12
