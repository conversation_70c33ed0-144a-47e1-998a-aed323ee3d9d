#!/usr/bin/env python3
"""
Test script to verify live speed functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'manoeuvres_simulation', 'src'))

from PyQt5.QtWidgets import QApplication
from gui import MainWindow

def test_live_speed_detection():
    """Test if live speed detection works correctly"""
    app = QApplication(sys.argv)
    window = MainWindow()
    
    # Test 1: Check if speed slider exists
    print("🧪 Test 1: Checking speed slider...")
    assert hasattr(window.palette, 'speed_slider'), "Speed slider not found!"
    print("✅ Speed slider found")
    
    # Test 2: Check if live speed indicator exists
    print("🧪 Test 2: Checking live speed indicator...")
    assert hasattr(window.palette, 'live_speed_indicator'), "Live speed indicator not found!"
    print("✅ Live speed indicator found")
    
    # Test 3: Simulate PowerPoint animation state
    print("🧪 Test 3: Simulating PowerPoint animation state...")
    window.animation_in_progress = True
    window.sequence_state = 'Playing'
    
    # Test 4: Trigger speed change
    print("🧪 Test 4: Triggering speed change...")
    window.on_speed_slider_changed(3)  # Should set speed to x6
    
    print("✅ All tests completed!")
    print("🔍 Check console output above for debug messages")
    
    app.quit()

if __name__ == "__main__":
    test_live_speed_detection()
