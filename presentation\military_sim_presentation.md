# Military Maneuvers Simulation System
## Interactive Tactical Visualization Platform

---

<!-- .slide: data-background="linear-gradient(to bottom right, #1a2a6c, #b21f1f)" -->
### System Evolution & Architecture
![System Evolution](./assets/images/evolution.svg)
- Version 1.0 → 5.0 Journey
- Core Technology Stack
- Key Milestones

---

<!-- .slide: data-transition="zoom" -->
### Core Technologies
- 🐍 Python 3.x
- 🎨 PyQt5 GUI Framework
- 🗺️ GDAL GeoTIFF Processing
- 📊 NumPy Calculations
- 🎬 Custom Animation System

---

<!-- .slide: data-transition="slide" -->
### Military Unit Hierarchy
```mermaid
graph TD
    A[Brigade] --> B[Groupement]
    B --> C[Battalion]
    C --> D[Sous-groupement]
    D --> E[Company]
    E --> F[Section]
    style A fill:#4a90e2
    style B fill:#50e3c2
    style C fill:#f5a623
    style D fill:#b8e986
    style E fill:#e74c3c
    style F fill:#c0392b
```

---

<!-- .slide: data-transition="fade" -->
### Range Visualization System
![Range Visualization](./assets/images/range_vis.svg)
- Anti-Personnel (AP)
- Anti-Car (AC)
- Missile Systems

---

<!-- .slide: data-background="#000" -->
### Line of Sight (LOS) System
- 🎯 Real-time Terrain Analysis
- 📡 360° Ray Casting
- 🗺️ Elevation Consideration
- 🎨 Dynamic Visualization

---

<!-- .slide: data-transition="convex" -->
### Animation Framework
![Animation System](./assets/images/animation.svg)
- Unit Movement
- Range Updates
- Status Changes
- Tactical Markers

---

<!-- .slide: data-transition="concave" -->
### Technical Implementation
```python
class AnimatedRadarLOS:
    # Radar Animation Implementation
    def __init__(self):
        self.animation_active = True
        self.refresh_rate = 30ms
```

---

<!-- .slide: data-background-gradient="linear-gradient(45deg, #12c2e9, #c471ed, #f64f59)" -->
### Future Roadmap
1. 🤖 AI Integration
2. 🌍 Enhanced Terrain Analysis
3. 📊 Multi-scenario Support
4. 🎮 Advanced Animation

---

<!-- .slide: data-transition="fade" -->
### Thank You
Contact: [Contact Information]
Project Version: 5.0
