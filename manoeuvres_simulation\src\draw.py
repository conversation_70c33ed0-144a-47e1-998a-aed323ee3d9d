# src/draw.py

from PyQt5.QtWidgets import (
    QGraphicsView, QGraphicsScene, QFrame, QSizePolicy,
    QGraphicsPixmapItem, QGraphicsEllipseItem, QGraphicsRectItem,
    QGraphicsLineItem, QGraphicsPathItem, QGraphicsItem, QGraphicsItemGroup,
    QGraphicsTextItem, QInputDialog
)
from PyQt5.QtCore import Qt, QMimeData, QTimer, pyqtSignal, QPointF, QLineF, QRectF
from PyQt5.QtGui import QPainter, QPen, QPainterPath, QColor, QBrush, QTransform
import sys
import math

class SimulationView(QGraphicsView):
    # Signals
    scene_clicked = pyqtSignal(QPointF)
    drawing_finished = pyqtSignal(object)
    point_selected = pyqtSignal(QPointF)
    # Fresh trajectory signal
    trajectory_completed = pyqtSignal(str, list)  # unit_name, points


    # Drawing mode constants
    MODE_SELECT = "Select Tool"
    MODE_TRAJECTORY = "Draw Trajectory"
    MODE_POINT = "Add Point (+)"
    MODE_LINE = "Draw Line"
    MODE_TEXT = "Add Text (A)"
    MODE_TEXT_FLOAT = "Text Float"

    def __init__(self, parent=None):
        super().__init__(parent)

        # Create and set the scene
        self.scene = QGraphicsScene()
        self.setScene(self.scene)

        # View settings
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setViewportUpdateMode(QGraphicsView.SmartViewportUpdate)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.verticalScrollBar().setDisabled(True)
        self.verticalScrollBar().hide()
        self.setOptimizationFlag(QGraphicsView.DontAdjustForAntialiasing, True)
        self.setOptimizationFlag(QGraphicsView.DontSavePainterState, True)
        self.setFrameStyle(0)
        
        # Initialize layers
        self.geotiff_layer = self.scene.createItemGroup([])
        self.content_layer = self.scene.createItemGroup([])
        self.geotiff_layer.setZValue(0)
        self.content_layer.setZValue(1)
        
        # Drawing state
        self.drawing_mode = self.MODE_SELECT
        self.is_drawing = False
        self.start_point = None
        self.current_item = None
        self.drawn_items = []

        # Fresh trajectory system
        self.trajectory_mode_active = False
        self.trajectory_target_unit = None
        self.trajectory_points = []
        self.trajectory_line_item = None


        
        # Drawing settings
        self.drawing_color = QColor(Qt.blue)  # Default color is now blue
        self.point_size = 2
        self.line_width = 8  # Default size is now 8 px
        self.text_font_size = 16  # Default font size for text
        
        # Enable drops
        self.setAcceptDrops(True)

        # Set initial scene rect to match the view size
        self.setSceneRect(QRectF(self.viewport().rect()))
        
        # Point selection mode flag
        self.is_point_selection_mode = False
        
        # Add tracking for selected items
        self.currently_selected_items = set()

    def _enable_military_unit_selection(self, enabled):
        """Enable or disable selection for military units (SimulationObject graphics)."""
        # Get main window to access military objects
        main_win = self.parentWidget().window()
        if hasattr(main_win, 'objects'):
            for obj in main_win.objects:
                if hasattr(obj, 'graphic') and obj.graphic:
                    obj.graphic.setFlag(obj.graphic.ItemIsSelectable, enabled)
                    obj.graphic.setFlag(obj.graphic.ItemIsMovable, enabled)
                    obj.graphic.setFlag(obj.graphic.ItemIsFocusable, enabled)
                    obj.graphic.setAcceptHoverEvents(enabled)
            print(f"[DEBUG] Military unit selection {'enabled' if enabled else 'disabled'} for {len(main_win.objects) if hasattr(main_win, 'objects') else 0} objects")

    def debug_selection_state(self):
        """Debug function to check current selection state."""
        print("\n=== SELECTION DEBUG INFO ===")
        print(f"Current drawing mode: {self.drawing_mode}")
        print(f"Scene selected items: {len(self.scene.selectedItems())}")

        main_win = self.parentWidget().window()
        if hasattr(main_win, 'objects'):
            print(f"Total military objects: {len(main_win.objects)}")
            for i, obj in enumerate(main_win.objects):
                if hasattr(obj, 'graphic') and obj.graphic:
                    flags = obj.graphic.flags()
                    selectable = bool(flags & obj.graphic.ItemIsSelectable)
                    movable = bool(flags & obj.graphic.ItemIsMovable)
                    selected = obj.graphic.isSelected()
                    print(f"  {i+1}. {obj.name}: selectable={selectable}, movable={movable}, selected={selected}")
        print("=== END DEBUG INFO ===\n")

    def add_to_layer(self, item, layer_type):
        """Add items to specific layers with proper georeferencing"""
        if layer_type == "geotiff":
            self.geotiff_layer.addToGroup(item)
            rect = item.boundingRect()
            self.scene.setSceneRect(rect)
            self.fit_to_rect(rect)
        elif layer_type in ["png", "drawing"]:
            # Both PNG and drawings go to content layer
            self.content_layer.addToGroup(item)
            
    def fit_to_rect(self, rect):
        """Fits the view to a specific rectangle while maintaining aspect ratio."""
        self.fitInView(rect, Qt.KeepAspectRatio)
        self.updateSceneRect()

    def updateSceneRect(self):
        """Update scene rectangle to match view bounds"""
        viewRect = self.viewport().rect()
        sceneRect = self.mapToScene(viewRect).boundingRect()
        currentRect = self.scene.sceneRect()
        newRect = currentRect.united(sceneRect)
        self.scene.setSceneRect(newRect)
        return newRect

    def isPositionInBounds(self, scene_pos):
        """Check if a position is within the GeoTIFF map boundaries"""
        # Find the GeoTIFF item in the geotiff layer
        geotiff_items = self.geotiff_layer.childItems()
        if not geotiff_items:
            return False  # If no GeoTIFF is loaded, consider out of bounds
            
        # Get the GeoTIFF boundaries
        geotiff_item = geotiff_items[0]  # First item should be the GeoTIFF
        map_rect = geotiff_item.boundingRect()
        map_pos = geotiff_item.pos()
        
        # Create rectangle representing map boundaries
        map_bounds = QRectF(
            map_pos.x(),
            map_pos.y(),
            map_rect.width(),
            map_rect.height()
        )
        
        # Check if position is within map boundaries
        return map_bounds.contains(scene_pos)

    def set_drawing_mode(self, mode):
        """Set the current drawing mode."""
        print(f"Setting drawing mode to: {mode}")
        self.drawing_mode = mode
        self.is_point_selection_mode = (mode == self.MODE_POINT)

        # Update cursor and drag mode based on selected tool
        if mode == self.MODE_SELECT:
            self.setDragMode(QGraphicsView.RubberBandDrag)
            self.setCursor(Qt.ArrowCursor)
            # Enable selection for all items
            for item in self.drawn_items:
                self.make_item_selectable(item, True)
            # Also enable selection for all military units
            self._enable_military_unit_selection(True)
        elif mode == self.MODE_TRAJECTORY:
            # Fresh trajectory mode setup
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(Qt.CrossCursor)
            # Enable military unit selection for trajectory drawing
            self._enable_military_unit_selection(True)
            # Disable selection for drawn items
            for item in self.drawn_items:
                self.make_item_selectable(item, False)
            print("🎯 Fresh trajectory mode activated")

        elif mode == self.MODE_TEXT:
            self.setCursor(Qt.IBeamCursor)
        elif mode == self.MODE_TEXT_FLOAT:
            self.setCursor(Qt.CrossCursor)
        else:
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(Qt.CrossCursor)
            # Disable selection while drawing
            for item in self.drawn_items:
                self.make_item_selectable(item, False)
            # Also disable military unit selection for other drawing modes
            self._enable_military_unit_selection(False)

        # Reset any ongoing drawing
        self.is_drawing = False
        self.start_point = None
        if self.current_item:
            self.content_layer.removeFromGroup(self.current_item)
            self.scene.removeItem(self.current_item)
            self.current_item = None
        
        # Force scene update
        self.scene.update()

    def make_item_selectable(self, item, selectable):
        from PyQt5.QtWidgets import QGraphicsTextItem
        if isinstance(item, QGraphicsItemGroup):
            item.setFlag(QGraphicsItem.ItemIsSelectable, selectable)
            item.setFlag(QGraphicsItem.ItemIsMovable, selectable)
            item.setFlag(QGraphicsItem.ItemIsFocusable, selectable)
            item.setAcceptHoverEvents(selectable)
            if selectable:
                item.setData(Qt.UserRole, "selectable")
                for child in item.childItems():
                    child.setFlag(QGraphicsItem.ItemIsSelectable, selectable)
                    child.setFlag(QGraphicsItem.ItemIsFocusable, selectable)
                    child.setAcceptHoverEvents(selectable)
                    if not child.data(Qt.UserRole + 1) and hasattr(child, 'pen'):
                        child.setData(Qt.UserRole + 1, child.pen())
        elif isinstance(item, QGraphicsTextItem):
            print(f"[DEBUG] make_item_selectable called for text: {item.toPlainText()}, selectable={selectable}")
            item.setFlag(QGraphicsItem.ItemIsSelectable, selectable)
            item.setFlag(QGraphicsItem.ItemIsMovable, selectable)
            item.setFlag(QGraphicsItem.ItemIsFocusable, selectable)
            item.setAcceptHoverEvents(selectable)
            if selectable:
                item.setData(Qt.UserRole, "selectable")
                item.setData(Qt.UserRole + 2, "Text")
            # Do NOT try to access .pen() or setData(Qt.UserRole + 1, ...)
        else:
            item.setFlag(QGraphicsItem.ItemIsSelectable, selectable)
            item.setFlag(QGraphicsItem.ItemIsMovable, selectable)
            item.setFlag(QGraphicsItem.ItemIsFocusable, selectable)
            item.setAcceptHoverEvents(selectable)
            if selectable:
                item.setData(Qt.UserRole, "selectable")
                if not item.data(Qt.UserRole + 1) and hasattr(item, 'pen'):
                    item.setData(Qt.UserRole + 1, item.pen())

    def undo_last_draw(self):
        """Removes the last drawn item."""
        if self.drawn_items:
            item = self.drawn_items.pop()
            self.scene.removeItem(item)
            print("Undid last drawing")

    def remove_selected_draw(self):
        """Removes the selected drawn items."""
        # Use our tracked selected items
        selected_items = list(self.currently_selected_items)
        
        if not selected_items:
            print("No items selected")
            return

        print(f"Found {len(selected_items)} selected items to remove")
        removed_count = 0
        
        for item in selected_items:
            # Skip if item is in geotiff_layer
            if self.geotiff_layer.isAncestorOf(item):
                continue
                
            # Skip if item is a background or overlay item
            if item.data(Qt.UserRole) != "selectable":
                continue

            print(f"Removing item of type: {type(item)}")
            try:
                # Remove from tracking set
                self.currently_selected_items.discard(item)
                
                # Remove from drawn items if present
                if item in self.drawn_items:
                    self.drawn_items.remove(item)
                    self.content_layer.removeFromGroup(item)
                    self.scene.removeItem(item)
                    removed_count += 1
                    
                    # If item is a group, ensure all children are removed
                    if isinstance(item, QGraphicsItemGroup):
                        for child in item.childItems():
                            if child in self.scene.items():
                                self.scene.removeItem(child)
                
            except Exception as e:
                print(f"Error removing item: {str(e)}")
        
        print(f"Successfully removed {removed_count} drawn items")
        self.scene.update()

    def set_drawing_color(self, color):
        """Sets the current drawing color."""
        self.drawing_color = color
        # Update current item if it exists
        if self.current_item:
            pen = self.current_item.pen()
            pen.setColor(color)
            self.current_item.setPen(pen)

    def set_drawing_size(self, size):
        """Sets the current drawing size."""
        self.line_width = size
        # Set font size for text
        if size <= 2:
            self.text_font_size = 12
        elif size <= 4:
            self.text_font_size = 16
        else:
            self.text_font_size = 22
        # Update current item if it exists
        if self.current_item:
            pen = self.current_item.pen()
            pen.setWidth(self.line_width)
            self.current_item.setPen(pen)
        # Update selected text items
        for item in self.scene.selectedItems():
            if isinstance(item, QGraphicsTextItem):
                font = item.font()
                font.setPointSize(self.text_font_size)
                item.setFont(font)

    def create_point_symbol(self, pos):
        """Create a + symbol at the given position."""
        group = QGraphicsItemGroup()
        self.scene.addItem(group)
        
        # Create the + symbol using two lines
        horizontal = QGraphicsLineItem(
            pos.x() - self.point_size/2, pos.y(),
            pos.x() + self.point_size/2, pos.y()
        )
        vertical = QGraphicsLineItem(
            pos.x(), pos.y() - self.point_size/2,
            pos.x(), pos.y() + self.point_size/2
        )
        
        # Set the pen for both lines
        pen = QPen(self.drawing_color, self.line_width)
        horizontal.setPen(pen)
        vertical.setPen(pen)
        
        # Add hover handlers to the lines
        horizontal.setAcceptHoverEvents(True)
        vertical.setAcceptHoverEvents(True)
        
        group.addToGroup(horizontal)
        group.addToGroup(vertical)
        
        # Make the group selectable with visual feedback
        self.make_item_selectable(group, True)
        
        # Add type information for hover feedback
        group.setData(Qt.UserRole + 2, "Point Symbol")
        
        return group

    def mousePressEvent(self, event):
        """Handle mouse press events for drawing and object selection."""
        if event.button() != Qt.LeftButton:
            super().mousePressEvent(event)
            return
        scene_pos = self.mapToScene(event.pos())
        print(f"[DEBUG] mousePressEvent at {scene_pos}, mode={self.drawing_mode}")
        if self.drawing_mode == self.MODE_TEXT_FLOAT:
            if not self.is_drawing:
                self.is_drawing = True
                self.start_point = scene_pos
                self.current_item = None
                return
            else:
                end_point = scene_pos
                mid_point = QPointF((self.start_point.x() + end_point.x()) / 2, (self.start_point.y() + end_point.y()) / 2)
                angle = math.degrees(math.atan2(end_point.y() - self.start_point.y(), end_point.x() - self.start_point.x()))
                text, ok = self.get_text_from_user()
                if ok and text:
                    item = RotatableMovableTextItem(text)
                    item.setDefaultTextColor(self.drawing_color)
                    font = item.font()
                    font.setPointSize(self.text_font_size)
                    item.setFont(font)
                    item.setPos(mid_point)
                    item.setRotation(angle)
                    self.scene.addItem(item)
                    self.drawn_items.append(item)
                    self.make_item_selectable(item, True)
                    self.set_drawing_mode(self.MODE_SELECT)
                self.is_drawing = False
                self.start_point = None
                self.current_item = None
                return
        # In select mode, handle selection
        if self.drawing_mode == self.MODE_SELECT:
            item = self.scene.itemAt(scene_pos, self.transform())
            print(f"[DEBUG] itemAt: {item}, type={type(item)}")
            if item:
                print(f"[DEBUG] Item flags: {item.flags()}, isSelectable={item.flags() & item.ItemIsSelectable}, isMovable={item.flags() & item.ItemIsMovable}")
                # If this is a user object (movable/interactive), let Qt handle selection/movement
                if item.flags() & (item.ItemIsSelectable | item.ItemIsMovable):
                    # print("[DEBUG] Qt selection/movement path")
                    super().mousePressEvent(event)
                    # --- FIX: Only keep currently selected items ---
                    self.currently_selected_items.clear()
                    for sel in self.scene.selectedItems():
                        self.currently_selected_items.add(sel)
                    # print(f"[DEBUG] After super: item.selected={item.isSelected()}, currently_selected_items={self.currently_selected_items}")
                    # --- FORCE selection for text if not selected ---
                    from PyQt5.QtWidgets import QGraphicsTextItem
                    if isinstance(item, QGraphicsTextItem) and not item.isSelected():
                        # print(f"[DEBUG] Forcing selection for text item: {item.toPlainText()}")
                        item.setSelected(True)
                        self.currently_selected_items.add(item)
                        # print(f"[DEBUG] After force: item.selected={item.isSelected()}, currently_selected_items={self.currently_selected_items}")
                    return
                # Otherwise, custom logic for drawn items (lines, points, etc.)
                original_item = item
                while item and not item.data(Qt.UserRole) == "selectable":
                    item = item.parentItem()
                # print(f"[DEBUG] After parent walk: {item}, type={type(item)}")
                if item and not self.geotiff_layer.isAncestorOf(item):
                    # print("[DEBUG] Custom selection path")
                    self.scene.clearSelection()
                    self.currently_selected_items.clear()
                    item.setSelected(True)
                    self.currently_selected_items.add(item)
                    if item.parentItem():
                        item.parentItem().setSelected(True)
                        self.currently_selected_items.add(item.parentItem())
                    item_type = item.data(Qt.UserRole + 2) or "Drawing"
                    # print(f"[DEBUG] Selected {item_type}, selected={item.isSelected()}, currently_selected_items={self.currently_selected_items}")
                    self.update_status_message(f"Selected {item_type}")
                    event.accept()
                    return
            # Only call super() for rubber band selection
            if not item:
                # print("[DEBUG] No item at click, calling super for rubber band")
                super().mousePressEvent(event)
                # --- FIX: Only keep currently selected items ---
                self.currently_selected_items.clear()
                for sel in self.scene.selectedItems():
                    self.currently_selected_items.add(sel)
            return
        # Check bounds for drawing modes
        if not self.isPositionInBounds(scene_pos):
            # print("[DEBUG] Out of bounds for drawing")
            return
        if self.drawing_mode == self.MODE_POINT:
            point = self.create_point_symbol(scene_pos)
            self.content_layer.addToGroup(point)
            self.drawn_items.append(point)
            self.point_selected.emit(scene_pos)
        elif self.drawing_mode == self.MODE_TRAJECTORY:
            self._handle_trajectory_click(scene_pos)


        elif self.drawing_mode == self.MODE_LINE:
            self.is_drawing = True
            self.start_point = scene_pos
            pen = QPen(self.drawing_color, self.line_width)
            line = HighlightableLineItem(
                scene_pos.x(), scene_pos.y(),
                scene_pos.x(), scene_pos.y()
            )
            line.setPen(pen)
            self.make_item_selectable(line, True)
            self.scene.addItem(line)
            line.setZValue(10)
            # print(f"[DEBUG] Added line to scene, parent={line.parentItem()}, z={line.zValue()}, in topLevelItems={line in self.scene.items()}")
            self.current_item = line
            # print(f"[DEBUG] Created line: {line}, flags={line.flags()}, isSelectable={line.flags() & line.ItemIsSelectable}, isMovable={line.flags() & line.ItemIsMovable}")
        elif self.drawing_mode == self.MODE_TEXT:
            text, ok = self.get_text_from_user()
            if ok and text:
                text_item = TransformableTextItem(text)
                text_item.setDefaultTextColor(self.drawing_color)
                font = text_item.font()
                font.setPointSize(self.text_font_size)
                text_item.setFont(font)
                text_item.setPos(scene_pos)
                self.content_layer.addToGroup(text_item)
                self.drawn_items.append(text_item)
                text_item.setData(Qt.UserRole, "selectable")
                text_item.setData(Qt.UserRole + 2, "Text")
                # print(f"[DEBUG] Set text item data: selectable, type=Text")
                self.make_item_selectable(text_item, True)
                self.set_drawing_mode(self.MODE_SELECT)
            return

    def mouseMoveEvent(self, event):
        """Handle mouse movement for drawing."""
        current_pos = self.mapToScene(event.pos())
        if self.is_drawing and self.current_item and self.start_point:
            # Update line while drawing
            if isinstance(self.current_item, QGraphicsLineItem):
                self.current_item.setLine(
                    self.start_point.x(),
                    self.start_point.y(),
                    current_pos.x(),
                    current_pos.y()
                )
                # print(f"Updating line to {current_pos}")  # Debug print
                # Force scene update
                self.scene.update()
        else:
           super().mouseMoveEvent(event)
            # Emit position for coordinate tracking if not drawing
        if not self.scene.mouseGrabberItem():
                self.scene_clicked.emit(current_pos)

    def _handle_trajectory_click(self, scene_pos):
        """Fresh trajectory click handler - builds continuous lines"""
        if not self.trajectory_mode_active:
            # Start new trajectory - need to select a unit first
            selected_items = self.scene.selectedItems()
            if not selected_items:
                self.update_status_message("🎯 Select a military unit first!")
                return

            # Find the military unit
            main_win = self.parentWidget().window()
            target_unit = None

            for item in selected_items:
                # Check if this item belongs to a military unit
                if hasattr(main_win, 'objects'):
                    for obj in main_win.objects:
                        if hasattr(obj, 'graphic') and obj.graphic == item:
                            target_unit = obj
                            break
                if target_unit:
                    break

            if not target_unit:
                self.update_status_message("❌ Please select a military unit!")
                return

            # Start trajectory
            self.trajectory_mode_active = True
            self.trajectory_target_unit = target_unit
            self.trajectory_points = [scene_pos]

            # Create visual line item with dashed gray style
            self.trajectory_line_item = QGraphicsPathItem()
            path = QPainterPath()
            path.addEllipse(scene_pos.x()-2, scene_pos.y()-2, 4, 4)  # Start dot (smaller)
            self.trajectory_line_item.setPath(path)

            # Create dashed gray pen
            pen = QPen(QColor("#95a5a6"), 2, Qt.DashLine)  # Gray color, 2px width, dashed
            pen.setDashPattern([4, 4])  # 4px dash, 4px gap
            self.trajectory_line_item.setPen(pen)
            self.trajectory_line_item.setZValue(1000)
            self.scene.addItem(self.trajectory_line_item)
            self.drawn_items.append(self.trajectory_line_item)

            print(f"🎯 Started trajectory for {target_unit.name}")
            self.update_status_message(f"🎯 Drawing trajectory for {target_unit.name} - Click to add points, ⚠️ RIGHT-CLICK or PRESS ENTER TO FINISH! ⚠️")

        else:
            # Add point to existing trajectory
            self.trajectory_points.append(scene_pos)
            print(f"📍 Added point {len(self.trajectory_points)}: ({scene_pos.x():.1f}, {scene_pos.y():.1f})")

            # Update visual - create continuous dashed line
            path = QPainterPath(self.trajectory_points[0])
            for point in self.trajectory_points[1:]:
                path.lineTo(point)

            self.trajectory_line_item.setPath(path)

            # Apply dashed gray pen
            pen = QPen(QColor("#95a5a6"), 2, Qt.DashLine)  # Gray color, 2px width, dashed
            pen.setDashPattern([4, 4])  # 4px dash, 4px gap
            self.trajectory_line_item.setPen(pen)

            self.update_status_message(f"🎯 Trajectory: {len(self.trajectory_points)} points - ⚠️ RIGHT-CLICK or PRESS ENTER TO FINISH! ⚠️")

    def mouseReleaseEvent(self, event):
        """Handle mouse release to finalize drawing."""
        print(f"[DEBUG] mouseReleaseEvent: button={event.button()}, mode={self.drawing_mode}, trajectory_active={getattr(self, 'trajectory_mode_active', False)}")

        # Handle trajectory completion with right-click
        if (event.button() == Qt.RightButton and
            self.drawing_mode == self.MODE_TRAJECTORY and
            self.trajectory_mode_active):

            print(f"🎯 RIGHT-CLICK DETECTED! Finishing trajectory with {len(self.trajectory_points)} points")

            if len(self.trajectory_points) < 2:
                self.update_status_message("❌ Need at least 2 points for trajectory!")
                return

            # Complete trajectory
            unit_name = self.trajectory_target_unit.name
            points_copy = self.trajectory_points.copy()

            print(f"✅ Trajectory completed: {unit_name} with {len(points_copy)} points")
            self.trajectory_completed.emit(unit_name, points_copy)

            # Reset trajectory state
            self.trajectory_mode_active = False
            self.trajectory_target_unit = None
            self.trajectory_points = []
            self.trajectory_line_item = None

            self.update_status_message(f"✅ Trajectory saved for {unit_name}!")
            self.set_drawing_mode(self.MODE_SELECT)
            return


        if event.button() == Qt.LeftButton:
            if self.drawing_mode == self.MODE_SELECT:
                # Update currently_selected_items with rubber band selection
                newly_selected = self.scene.selectedItems()
                if newly_selected:
                    # print(f"Selected {len(newly_selected)} items via rubber band")
                    for item in newly_selected:
                        self.currently_selected_items.add(item)
                return
            if self.is_drawing and self.current_item:
                current_pos = self.mapToScene(event.pos())
                if not self.isPositionInBounds(current_pos):
                    self.content_layer.removeFromGroup(self.current_item)
                    self.scene.removeItem(self.current_item)
                else:
                    self.drawn_items.append(self.current_item)
                    self.make_item_selectable(self.current_item, True)
                    self.drawing_finished.emit(self.current_item)
            # Reset drawing state
                self.start_point = None
            self.current_item = None
            self.is_drawing = False
            self.scene.update()
        else:
          super().mouseReleaseEvent(event)

    def keyPressEvent(self, event):
        """Handle keyboard events for trajectory completion."""
        if (event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter):
            if (self.drawing_mode == self.MODE_TRAJECTORY and
                self.trajectory_mode_active and
                len(self.trajectory_points) >= 2):

                print(f"🎯 ENTER KEY DETECTED! Finishing trajectory with {len(self.trajectory_points)} points")

                # Complete trajectory (same logic as right-click)
                unit_name = self.trajectory_target_unit.name
                points_copy = self.trajectory_points.copy()

                print(f"✅ Trajectory completed: {unit_name} with {len(points_copy)} points")
                self.trajectory_completed.emit(unit_name, points_copy)

                # Reset trajectory state
                self.trajectory_mode_active = False
                self.trajectory_target_unit = None
                self.trajectory_points = []
                self.trajectory_line_item = None

                self.update_status_message(f"✅ Trajectory saved for {unit_name}!")
                self.set_drawing_mode(self.MODE_SELECT)
                return

        super().keyPressEvent(event)

    def resizeEvent(self, event):
        """Handle view resize and keep scene rect matched to view size to prevent scrollbars."""
        super().resizeEvent(event)
        self.setSceneRect(QRectF(self.viewport().rect()))
        # If we have a GeoTIFF, fit to it
        if self.geotiff_layer.childItems():
            self.fit_to_rect(self.geotiff_layer.childItems()[0].boundingRect())
        # Otherwise if we have a PNG, fit to it
        elif self.content_layer.childItems():
            self.fit_to_rect(self.content_layer.childItems()[0].boundingRect())
        else:
            self.updateSceneRect()

    def showEvent(self, event):
        """Handle the initial show event."""
        super().showEvent(event)
        # Initial fitting of content
        if self.geotiff_layer.childItems():
            self.fit_to_rect(self.geotiff_layer.childItems()[0].boundingRect())
        elif self.content_layer.childItems():
            self.fit_to_rect(self.content_layer.childItems()[0].boundingRect())

    def dragEnterEvent(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move events with bounds checking."""
        if event.mimeData().hasText():
            scene_pos = self.mapToScene(event.pos())
            if self.isPositionInBounds(scene_pos):
              event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle dropping items with bounds checking."""
        if event.mimeData().hasText():
            scene_pos = self.mapToScene(event.pos())
            if self.isPositionInBounds(scene_pos):
                object_type = event.mimeData().text()
                main_win = self.parentWidget().window()
            if hasattr(main_win, "create_object_by_type"):
                main_win.create_object_by_type(object_type, (scene_pos.x(), scene_pos.y()))
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def update_status_message(self, message):
        """Update status bar message if main window exists."""
        main_win = self.parentWidget().window()
        if main_win and hasattr(main_win, 'statusBar'):
            main_win.statusBar().showMessage(message)
        print(f"[STATUS] {message}")  # Also print to console for debugging

    # Add these new methods for hover effects
    def hoverEnterEvent(self, event):
        """Handle hover enter events with visual feedback."""
        item = event.item()
        if item and item.data(Qt.UserRole) == "selectable":
            # Get original pen and create hover effect
            original_pen = item.data(Qt.UserRole + 1)
            if original_pen:
                hover_pen = QPen(original_pen)
                hover_pen.setWidth(hover_pen.width() + 1)
                hover_pen.setColor(QColor(255, 165, 0))  # Orange for hover
                item.setPen(hover_pen)
            
            # Show hover feedback in status bar
            item_type = item.data(Qt.UserRole + 2) or "Drawing"
            self.update_status_message(f"Hover: {item_type}")
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        """Handle hover leave events, restoring original appearance."""
        item = event.item()
        if item and item.data(Qt.UserRole) == "selectable":
            # Restore original pen
            original_pen = item.data(Qt.UserRole + 1)
            if original_pen:
                item.setPen(original_pen)
            
            # Clear status message
            self.update_status_message("")
        super().hoverLeaveEvent(event)

    def get_text_from_user(self):
        text, ok = QInputDialog.getText(self, "Add Text", "Enter text:")
        return text, ok

    def set_text_size(self, size):
        """Sets the font size for text drawing and updates selected text items."""
        self.text_font_size = size
        # Update selected text items
        for item in self.scene.selectedItems():
            if isinstance(item, QGraphicsTextItem):
                font = item.font()
                font.setPointSize(size)
                item.setFont(font)
        # If currently drawing a text item, update its font size as well
        if self.current_item and isinstance(self.current_item, QGraphicsTextItem):
            font = self.current_item.font()
            font.setPointSize(size)
            self.current_item.setFont(font)

class HighlightableLineItem(QGraphicsLineItem):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setFlag(QGraphicsLineItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsLineItem.ItemIsMovable, True)
        self.setFlag(QGraphicsLineItem.ItemIsFocusable, True)
        self.setAcceptHoverEvents(True)
        self._normal_pen = QPen(Qt.red, 2)
        self._highlight_pen = QPen(Qt.blue, 4)
        self._highlight_pen.setColor(QColor(0, 120, 255, 180))
        self._shadow_pen = QPen(QColor(0,0,0,80), 8)
    def setPen(self, pen):
        self._normal_pen = pen
        super().setPen(pen)
    def paint(self, painter, option, widget=None):
        if self.isSelected():
            painter.setPen(self._shadow_pen)
            painter.drawLine(self.line())
            painter.setPen(self._highlight_pen)
            painter.drawLine(self.line())
        else:
            painter.setPen(self._normal_pen)
            painter.drawLine(self.line())

class TransformableTextItem(QGraphicsTextItem):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setFlags(
            QGraphicsTextItem.ItemIsSelectable |
            QGraphicsTextItem.ItemIsMovable |
            QGraphicsTextItem.ItemSendsGeometryChanges |
            QGraphicsTextItem.ItemIsFocusable
        )
        self.setAcceptHoverEvents(True)
        self.setAcceptedMouseButtons(Qt.LeftButton)
        self._handle_size = 10
        self._rotating = False
        self._last_mouse_pos = None
        self._update_handles()

    def _update_handles(self):
        rect = self.boundingRect()
        self._rotate_handle_rect = QRectF(rect.center().x() - self._handle_size/2, rect.top() - 2*self._handle_size, self._handle_size, self._handle_size)
        self.prepareGeometryChange()
        self.update()

    def paint(self, painter, option, widget=None):
        super().paint(painter, option, widget)
        if self.isSelected():
            pen = QPen(Qt.DashLine)
            pen.setColor(Qt.blue)
            painter.setPen(pen)
            painter.drawRect(self.boundingRect())
            painter.setBrush(QBrush(Qt.red))
            painter.drawEllipse(self._rotate_handle_rect)

    def mousePressEvent(self, event):
        # print(f"[DEBUG] mousePressEvent: pos={event.pos()}, scenePos={event.scenePos()}, selected={self.isSelected()}")
        if self._rotate_handle_rect.contains(event.pos()):
            # print("[DEBUG] Rotate handle pressed")
                self._rotating = True
                self._last_mouse_pos = event.scenePos()
                event.accept()
                return
        # Let Qt handle move/select for the main area
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self._rotating and self._last_mouse_pos:
            # print("[DEBUG] Rotating text")
            center = self.mapToScene(self.boundingRect().center())
            last_angle = math.atan2(self._last_mouse_pos.y() - center.y(), self._last_mouse_pos.x() - center.x())
            new_angle = math.atan2(event.scenePos().y() - center.y(), event.scenePos().x() - center.x())
            angle_diff = math.degrees(new_angle - last_angle)
            self.setRotation(self.rotation() + angle_diff)
            self._last_mouse_pos = event.scenePos()
            self._update_handles()
            event.accept()
            return
        # Let Qt handle move for the main area
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        self._rotating = False
        self._last_mouse_pos = None
        self._update_handles()
        super().mouseReleaseEvent(event)

    def hoverMoveEvent(self, event):
        if self._rotate_handle_rect.contains(event.pos()):
            self.setCursor(Qt.OpenHandCursor)
        else:
            self.setCursor(Qt.IBeamCursor)
        super().hoverMoveEvent(event)

class RotatableMovableTextItem(QGraphicsTextItem):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setFlags(
            QGraphicsTextItem.ItemIsSelectable |
            QGraphicsTextItem.ItemIsMovable |
            QGraphicsTextItem.ItemSendsGeometryChanges |
            QGraphicsTextItem.ItemIsFocusable
        )
        self.setAcceptHoverEvents(True)
        self.setAcceptedMouseButtons(Qt.LeftButton)
        self._handle_size = 10
        self._rotating = False
        self._last_mouse_pos = None
        self._update_handles()
    def _update_handles(self):
        rect = self.boundingRect()
        self._rotate_handle_rect = QRectF(rect.center().x() - self._handle_size/2, rect.top() - 2*self._handle_size, self._handle_size, self._handle_size)
        self.prepareGeometryChange()
        self.update()
    def paint(self, painter, option, widget=None):
        super().paint(painter, option, widget)
        if self.isSelected():
            pen = QPen(Qt.DashLine)
            pen.setColor(Qt.blue)
            painter.setPen(pen)
            painter.drawRect(self.boundingRect())
            painter.setBrush(QBrush(Qt.red))
            painter.drawEllipse(self._rotate_handle_rect)
    def mousePressEvent(self, event):
        if self._rotate_handle_rect.contains(event.pos()):
            self._rotating = True
            self._last_mouse_pos = event.scenePos()
            event.accept()
            return
        super().mousePressEvent(event)
    def mouseMoveEvent(self, event):
        if self._rotating and self._last_mouse_pos:
            center = self.mapToScene(self.boundingRect().center())
            last_angle = math.atan2(self._last_mouse_pos.y() - center.y(), self._last_mouse_pos.x() - center.x())
            new_angle = math.atan2(event.scenePos().y() - center.y(), event.scenePos().x() - center.x())
            angle_diff = math.degrees(new_angle - last_angle)
            self.setRotation(self.rotation() + angle_diff)
            self._last_mouse_pos = event.scenePos()
            self._update_handles()
            event.accept()
            return
        super().mouseMoveEvent(event)
    def mouseReleaseEvent(self, event):
        self._rotating = False
        self._last_mouse_pos = None
        self._update_handles()
        super().mouseReleaseEvent(event)
    def hoverMoveEvent(self, event):
        if self._rotate_handle_rect.contains(event.pos()):
            self.setCursor(Qt.OpenHandCursor)
        else:
            self.setCursor(Qt.IBeamCursor)
        super().hoverMoveEvent(event)
