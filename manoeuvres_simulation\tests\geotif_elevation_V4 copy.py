import sys
import os
import math
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel,
                                 QLineEdit, QPushButton, QGridLayout, QFileDialog,
                                 QGraphicsView, QGraphicsScene, QGraphicsEllipseItem,QVBoxLayout,
                                 QMessageBox, QStatusBar, QToolBar, QAction, QHBoxLayout) # Added QToolBar, QAction, and QHBoxLayout
from PyQt5.QtCore import Qt, QPointF, QSize
from PyQt5.QtGui import QPixmap, QImage, QColor, QPen, QBrush, QIcon, QPainter # Added QIcon and QPainter
from osgeo import gdal  # Import GDAL library

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("GeoTIFF and Screenshot Collage")
        self.setGeometry(100, 100, 800, 600)

        # Initialize main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget) # Main layout is now QVBoxLayout for toolbar at top

        # Initialize variables
        self.ds = None
        self.geotransform = None
        self.image_array = None
        self.points = []
        self.display_pixmap_item = None  # Store QGraphicsPixmapItem for GeoTIFF
        self.screenshot_pixmap = None # To store loaded screenshot pixmap (QPixmap directly)
        self.collage_pixmap = None    # To store the created collage pixmap
        self.is_collage_displayed = False # Flag to track if collage is currently displayed

        # Create UI elements
        self.create_widgets()
        self.setup_layout()

    def create_widgets(self):
        """Initialize UI components including toolbar buttons."""
        # --- Toolbar ---
        tool_bar = QToolBar("Main Toolbar")
        self.addToolBar(tool_bar)

        # Load GeoTIFF button
        load_geotiff_action = QAction(QIcon(), "Load GeoTIFF", self)
        load_geotiff_action.setStatusTip("Load a GeoTIFF file")
        load_geotiff_action.triggered.connect(self.load_geotiff)
        tool_bar.addAction(load_geotiff_action)

        # Load Screenshot button
        load_screenshot_action = QAction(QIcon(), "Load Screenshot", self)
        load_screenshot_action.setStatusTip("Load a Screenshot image (16:9 aspect)")
        load_screenshot_action.triggered.connect(self.load_screenshot)
        tool_bar.addAction(load_screenshot_action)

        # Create Collage button
        create_collage_action = QAction(QIcon(), "Create Collage", self)
        create_collage_action.setStatusTip("Create a side-by-side collage of GeoTIFF and Screenshot")
        create_collage_action.triggered.connect(self.create_collage)
        tool_bar.addAction(create_collage_action)

        # Display GeoTIFF button (to switch back from collage)
        display_geotiff_action = QAction(QIcon(), "Display GeoTIFF", self)
        display_geotiff_action.setStatusTip("Display only the GeoTIFF image")
        display_geotiff_action.triggered.connect(self.display_geotiff_only)
        display_geotiff_action.setEnabled(False) # Initially disabled, enabled after creating collage
        self.display_geotiff_action = display_geotiff_action # Store action to toggle enable/disable
        tool_bar.addAction(display_geotiff_action)

        # Clear Points button
        self.btn_clear = QPushButton("Clear Points")

        self.label_status = QLabel("Status: Load a GeoTIFF file.")
        self.label_points = QLineEdit()
        self.label_points.setReadOnly(True)
        self.label_distance = QLabel("Distance: ")

        # Graphics view for displaying images
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_view.setFixedSize(800, int(800 / 16 * 9)) # Fixed size with 16:9 aspect ratio - **FIXED HERE**

        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

        # Connect signals
        self.btn_clear.clicked.connect(self.clear_points)
        self.graphics_view.mousePressEvent = self.mouse_press_event

    def setup_layout(self):
        """Arrange widgets in the vertical layout."""
        # Use the QVBoxLayout as the main layout
        toolbar_widget = QWidget() # Widget to contain buttons horizontally
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.addWidget(self.btn_clear) # Clear Points Button in the widget based toolbar (below main toolbar)
        toolbar_widget.setLayout(toolbar_layout) # Set layout for the widget

        self.layout.addWidget(self.graphics_view) # Graphics View takes most of the space
        self.layout.addWidget(self.label_points)    # Points display below graphics view
        self.layout.addWidget(self.label_distance)  # Distance display
        self.layout.addWidget(self.label_status)    # Status label at the very bottom
        self.layout.addWidget(toolbar_widget)       # Add the horizontal toolbar widget to the main layout
        self.central_widget.setLayout(self.layout) # Set layout for central widget


    def load_geotiff(self):
        """Load GeoTIFF file and display it."""
        filepath, _ = QFileDialog.getOpenFileName(
            self, "Open GeoTIFF", "", "GeoTIFF files (*.tif *.tiff)")

        if not filepath:
            return

        self.status_bar.showMessage("Loading GeoTIFF...", 0)
        try:
            self.ds = gdal.Open(filepath)
            if self.ds is None:
                self.label_status.setText("Error: Could not open GeoTIFF file")
                QMessageBox.critical(self, "Error", "Could not open GeoTIFF file with GDAL.")
                self.status_bar.showMessage("Error loading GeoTIFF", 5000)
                return

            self.geotransform = self.ds.GetGeoTransform()
            band = self.ds.GetRasterBand(1)
            if band is None:
                self.label_status.setText("Error: Could not read raster band from GeoTIFF")
                QMessageBox.critical(self, "Error", "Could not read raster band from GeoTIFF.")
                self.status_bar.showMessage("Error loading GeoTIFF", 5000)
                self.ds = None
                return
            self.image_array = band.ReadAsArray()
            if self.image_array is None:
                self.label_status.setText("Error: Could not read raster data array.")
                QMessageBox.critical(self, "Error", "Could not read raster data array from GeoTIFF.")
                self.status_bar.showMessage("Error loading GeoTIFF", 5000)
                self.ds = None
                return

            self.create_display_image()
            self.label_status.setText(f"Loaded GeoTIFF: {os.path.basename(filepath)}")
            self.status_bar.showMessage(f"GeoTIFF '{os.path.basename(filepath)}' loaded.", 5000)
            self.clear_points()
            self.screenshot_pixmap = None # Clear screenshot pixmap when new GeoTIFF is loaded
            self.collage_pixmap = None
            self.is_collage_displayed = False
            self.display_geotiff_action.setEnabled(False) # Disable 'Display GeoTIFF' button

        except Exception as e:
            self.label_status.setText("Error: Problem loading GeoTIFF")
            QMessageBox.critical(self, "Loading Error", f"Failed to load GeoTIFF.\nError details: {e}")
            self.status_bar.showMessage("Error loading GeoTIFF", 5000)
            self.ds = None
            self.image_array = None
            self.geotransform = None

    def load_screenshot(self):
        """Load screenshot image file and store as pixmap."""
        filepath, _ = QFileDialog.getOpenFileName(
            self, "Open Screenshot Image", "", "Image files (*.png *.jpg *.jpeg *.bmp)")
        if not filepath:
            return

        self.status_bar.showMessage("Loading Screenshot...", 0)
        try:
            pixmap = QPixmap(filepath)
            if pixmap.isNull():
                self.label_status.setText("Error: Could not load screenshot image")
                QMessageBox.critical(self, "Error", "Could not load screenshot image. Invalid image file.")
                self.status_bar.showMessage("Error loading screenshot", 5000)
                return

            # Check for 16:9 aspect ratio (approximately) - can be adjusted for tolerance
            aspect_ratio = pixmap.width() / pixmap.height()
            if not (1.7 < aspect_ratio < 1.8): # Tolerance for 16:9 (16/9 = 1.777...)
                reply = QMessageBox.warning(self, "Aspect Ratio Warning",
                                            "Screenshot does not appear to be 16:9 aspect ratio. Continue anyway?",
                                            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    self.status_bar.showMessage("Screenshot loading cancelled due to aspect ratio.", 3000)
                    return # User chose not to continue

            self.screenshot_pixmap = pixmap
            self.label_status.setText(f"Loaded Screenshot: {os.path.basename(filepath)}")
            self.status_bar.showMessage(f"Screenshot '{os.path.basename(filepath)}' loaded.", 5000)
            self.collage_pixmap = None # Invalidate any previous collage when new screenshot is loaded
            self.is_collage_displayed = False
            self.display_geotiff_action.setEnabled(False) # Disable 'Display GeoTIFF' button

        except Exception as e:
            self.label_status.setText("Error: Problem loading screenshot")
            QMessageBox.critical(self, "Loading Error", f"Failed to load screenshot image.\nError details: {e}")
            self.status_bar.showMessage("Error loading screenshot", 5000)
            self.screenshot_pixmap = None

    def create_display_image(self):
        """Convert elevation data array to a displayable grayscale QPixmap."""
        if self.image_array is None:
            return

        data_min = np.nanmin(self.image_array)
        data_max = np.nanmax(self.image_array)
        if data_max <= data_min:
            normalized = np.zeros_like(self.image_array, dtype=np.uint8)
        else:
            normalized = (self.image_array - data_min) / (data_max - data_min) * 255
            normalized = normalized.astype(np.uint8)

        height, width = normalized.shape
        qimage = QImage(normalized.data, width, height, width, QImage.Format_Grayscale8)
        pixmap = QPixmap.fromImage(qimage)

        self.graphics_scene.clear()
        # Store QGraphicsPixmapItem, not QPixmap directly for GeoTIFF
        self.display_pixmap_item = self.graphics_scene.addPixmap(pixmap)
        self.graphics_view.fitInView(self.display_pixmap_item, Qt.KeepAspectRatio)

    def create_collage(self):
        """Create and display a side-by-side collage of GeoTIFF and screenshot."""
        if self.display_pixmap_item is None: # Check for display_pixmap_item instead of display_pixmap
            QMessageBox.warning(self, "Warning", "Load a GeoTIFF file first to create a collage.")
            self.status_bar.showMessage("GeoTIFF required for collage.", 3000)
            return
        if self.screenshot_pixmap is None:
            QMessageBox.warning(self, "Warning", "Load a Screenshot image first to create a collage.")
            self.status_bar.showMessage("Screenshot required for collage.", 3000)
            return

        try:
            # Get QPixmap from QGraphicsPixmapItem for GeoTIFF
            geotiff_pixmap = self.display_pixmap_item.pixmap()
            if geotiff_pixmap.isNull(): # Double check pixmap validity
                raise ValueError("GeoTIFF pixmap is invalid.")
            if self.screenshot_pixmap.isNull(): # Double check screenshot pixmap validity
                raise ValueError("Screenshot pixmap is invalid.")

            self.collage_pixmap = self.create_side_by_side_collage(geotiff_pixmap, self.screenshot_pixmap)
            self.graphics_scene.clear()
            self.graphics_scene.addPixmap(self.collage_pixmap)
            self.graphics_view.fitInView(self.graphics_scene.sceneRect(), Qt.KeepAspectRatio)
            self.label_status.setText("Collage created and displayed.")
            self.status_bar.showMessage("Collage created.", 5000)
            self.is_collage_displayed = True
            self.display_geotiff_action.setEnabled(True) # Enable 'Display GeoTIFF' button to switch back
        except Exception as e:
            QMessageBox.critical(self, "Collage Error", f"Failed to create collage.\nError details: {e}")
            self.status_bar.showMessage("Error creating collage.", 5000)
            self.collage_pixmap = None
            self.is_collage_displayed = False
            self.display_geotiff_action.setEnabled(False)


    def display_geotiff_only(self):
        """Switch back to displaying only the GeoTIFF image."""
        if self.display_pixmap_item: # Check for display_pixmap_item existence
            self.graphics_scene.clear()
            self.graphics_scene.addItem(self.display_pixmap_item) # Re-add the QGraphicsPixmapItem
            self.graphics_view.fitInView(self.graphics_scene.sceneRect(), Qt.KeepAspectRatio)
            self.label_status.setText("Displaying GeoTIFF only.")
            self.status_bar.showMessage("Displaying GeoTIFF.", 3000)
            self.is_collage_displayed = False
            self.display_geotiff_action.setEnabled(False) # Disable 'Display GeoTIFF' button as we are already displaying it

    def create_side_by_side_collage(self, geotiff_pixmap, screenshot_pixmap):
        """Create a side-by-side collage QPixmap."""
        if geotiff_pixmap is None or screenshot_pixmap is None: # Robust check for None pixmaps
            raise ValueError("One or both pixmaps are None. Cannot create collage.")

        tiff_width = geotiff_pixmap.width() # Now working with QPixmap, so .width() is valid
        tiff_height = geotiff_pixmap.height()
        screenshot_width = screenshot_pixmap.width()
        screenshot_height = screenshot_pixmap.height()

        collage_width = tiff_width + screenshot_width
        collage_height = max(tiff_height, screenshot_height)

        collage_pixmap = QPixmap(collage_width, collage_height)
        collage_pixmap.fill(Qt.white) # White background

        painter = QPainter(collage_pixmap)
        painter.drawPixmap(0, 0, geotiff_pixmap) # GeoTIFF on the left
        painter.drawPixmap(tiff_width, 0, screenshot_pixmap) # Screenshot on the right
        painter.end()
        return collage_pixmap


    def mouse_press_event(self, event):
        """Handle mouse clicks for point selection if collage is not displayed."""
        if self.is_collage_displayed:
            return # Disable point selection when collage is displayed
        if not self.display_pixmap_item or self.geotransform is None or self.image_array is None: # Check for display_pixmap_item
            return

        scene_pos = self.graphics_view.mapToScene(event.pos())
        item_pos = self.display_pixmap_item.mapFromScene(scene_pos) # Use display_pixmap_item
        x, y = item_pos.x(), item_pos.y()

        x_pixel = int(round(x))
        y_pixel = int(round(y))

        if not (0 <= x_pixel < self.image_array.shape[1] and 0 <= y_pixel < self.image_array.shape[0]):
            return

        lon = self.geotransform[0] + x_pixel * self.geotransform[1] + y_pixel * self.geotransform[2]
        lat = self.geotransform[3] + x_pixel * self.geotransform[4] + y_pixel * self.geotransform[5]

        try:
            z = float(self.image_array[y_pixel, x_pixel])
        except (IndexError, ValueError):
            z = np.nan

        if np.isnan(z):
            self.label_status.setText("Warning: No elevation data at selected point")
            self.status_bar.showMessage("No elevation data at selected point", 3000)
            return

        self.points.append((lon, lat, z))
        self.update_points_display()
        self.update_display_markers()

        if len(self.points) >= 2:
            self.calculate_distance()

        self.status_bar.showMessage(f"Point {len(self.points)} selected: Lon={lon:.6f}, Lat={lat:.6f}", 3000)

    def haversine(self, lon1, lat1, lon2, lat2):
        """Calculate horizontal distance."""
        lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])

        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371000
        return c * r

    def calculate_distance(self):
        """Calculate and display 3D distance."""
        if len(self.points) < 2:
            return

        p1 = self.points[-2]
        p2 = self.points[-1]

        lon1, lat1, z1 = p1
        lon2, lat2, z2 = p2

        horizontal_dist = self.haversine(lon1, lat1, lon2, lat2)
        vertical_diff = abs(z2 - z1)
        distance_3d = math.sqrt(horizontal_dist**2 + vertical_diff**2)

        self.label_distance.setText(
            f"3D Distance: {distance_3d:.2f} meters\n"
            f"Horizontal: {horizontal_dist:.2f} m | Vertical: {vertical_diff:.2f} m"
        )
        self.status_bar.showMessage("Distance calculated.", 3000)

    def update_points_display(self):
        """Update points display textbox."""
        text = "Selected Points (Longitude, Latitude, Elevation):\n"
        for i, (lon, lat, z) in enumerate(self.points, 1):
            text += f"Point {i}: Lon={lon:.6f}, Lat={lat:.6f}, Z={z:.2f}\n"
        self.label_points.setText(text)

    def update_display_markers(self):
        """Update point markers on map."""
        for item in self.graphics_scene.items():
            if isinstance(item, QGraphicsEllipseItem):
                self.graphics_scene.removeItem(item)

        pen = QPen(QColor(255, 0, 0))
        brush = QBrush(QColor(255, 0, 0))
        for lon, lat, _ in self.points:
            px = (lon - self.geotransform[0]) / self.geotransform[1]
            py = (lat - self.geotransform[3]) / self.geotransform[5]
            self.graphics_scene.addEllipse(px-3, py-3, 6, 6, pen, brush)

    def clear_points(self):
        """Clear selected points."""
        self.points = []
        self.label_points.clear()
        self.label_distance.setText("Distance: ")
        self.update_display_markers()
        self.status_bar.showMessage("Points cleared.", 3000)

    def resizeEvent(self, event):
        """Handle resize events."""
        if self.display_pixmap_item: # Check for display_pixmap_item
            self.graphics_view.fitInView(self.graphics_scene.sceneRect(), Qt.KeepAspectRatio)
        super().resizeEvent(event)

if __name__ == "__main__":
    try:
        gdal.UseExceptions()
        print("GDAL version:", gdal.VersionInfo())
    except Exception as e:
        print(f"Error importing GDAL or GDAL not properly configured: {e}")
        print("Please ensure GDAL is installed correctly and is in your system's PATH.")
        sys.exit(1)

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())