# src/gui.py

import os
import sys
import pyqt5_fugueicons as fugue 
import math # Added for GeoTIFF logic
import numpy as np # Added for GeoTIFF logic
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PyQt5.QtWidgets import (
    QMainWindow, QApplication, QTreeWidget, QTreeWidgetItem,
    QHBoxLayout, QVBoxLayout, QWidget, QPushButton,
    QDockWidget, QLabel, QLineEdit, QColorDialog, QGraphicsPixmapItem,
    QGroupBox, QListWidget, QListWidgetItem, QSizePolicy, QGraphicsRectItem,
    QFileDialog, QMessageBox, QTextEdit, QComboBox, QGraphicsView, QGraphicsScene,
    QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsPathItem, QDialog,
    QAction, QGraphicsTextItem, QInputDialog, QTabWidget, QCheckBox,
    QAbstractButton, QFrame, QGridLayout, QShortcut, QRadioButton, QButtonGroup,
    QProgressBar, QSlider, QSpinBox, QDoubleSpinBox
)
from PyQt5.QtCore import (
    Qt, QTimer, QMimeData, QSize, QPointF, QPropertyAnimation,
    QEasingCurve, pyqtProperty, QObject, QPoint, QParallelAnimationGroup,
    pyqtSignal, QRectF, Qt, QSequentialAnimationGroup
)
from PyQt5.QtGui import (
    QPixmap, QIcon, QBrush, QColor, QDrag, QFont,
    QImage, QPen, QPainter, QPainterPath, QKeySequence,
    QGuiApplication, QScreen, QPolygonF
)
from enum import Enum

# Attempt to import GDAL (Moved near top)
try:
    from osgeo import gdal
    GDAL_AVAILABLE = True
except ImportError:
    GDAL_AVAILABLE = False
    print("Warning: GDAL Python bindings are not installed or configured correctly. GeoTIFF functionality will be disabled.")
    # We'll show a message box later if the user tries to use the feature

# Import the custom SimulationView from draw.py
from draw import SimulationView

# -- Import your existing project modules --
from physics import PhysicsSpace
from objects import SimulationObject, gpt_moto, gpt_meca, S_gpt_moto, S_gpt_meca, enmi_brigade, enmi_company, enmi_section, Bataillon_meca, Bataillon_moto, Brigade_meca, Brigade_moto, Compagnie_meca, Compagnie_moto, Groupement_meca, Groupement_moto, Section_meca, Section_moto, Sous_groupement_meca, Sous_groupement_moto, Bataillon_meca_Ennemi, Bataillon_moto_Ennemi, Brigade_meca_Ennemi, Brigade_moto_Ennemi, Compagnie_meca_Ennemi, Compagnie_moto_Ennemi, Groupement_meca_Ennemi, Groupement_moto_Ennemi, Section_meca_Ennemi, Section_moto_Ennemi, Sous_groupement_meca_Ennemi, Sous_groupement_moto_Ennemi,Cas, Cml, Esc, Geb, Reco, Sac, Jeep_ami, Tank_ami, Jeep_ennemi, Tank_ennemi, M113_ami, M113_ennemi
from animation import AnimationManager

from project_setup_dialog import ProjectSetupDialog # Import the new dialog

# Custom Ellipse Item - Needed for GeoTIFF marker animation
# Inherits only from QGraphicsEllipseItem to avoid multiple inheritance issues
class AnimatedEllipseItem(QGraphicsEllipseItem):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Store the center point for scaling calculations
        self._center = self.boundingRect().center()

    # Method to apply scale transformation based on a value
    def setScaleValue(self, value):
        # Get current transform, apply scaling relative to center
        transform = self.transform()
        # Reset transform to identity before applying new scale from center
        transform.reset()
        transform.translate(self._center.x(), self._center.y())
        transform.scale(value, value)
        transform.translate(-self._center.x(), -self._center.y())
        self.setTransform(transform)

# ---------------------------------------------------
# Comprehensive Palette Widget
# ---------------------------------------------------
class ComprehensivePalette(QWidget):
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window  # Store direct reference to MainWindow
        # --- Initialize state flags before any method calls ---
        self.geotiff_loaded = False
        self.png_loaded = False
        self._csra_captured = False  # Defensive: always exists
        self.current_step = 1  # Start with step 1
        self.setup_ui()
        self.setSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.Preferred)

    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # ----------------------------
        # Step Indicator
        # ----------------------------
        self.step_group = QGroupBox("Setup Steps")
        step_layout = QVBoxLayout()
        self.step_group.setLayout(step_layout)

        # Step labels
        self.step1_label = QLabel("Step 1: Load Terrain (GeoTIFF & PNG)")
        self.step2_label = QLabel("Step 2: Project Setup")
        self.step3_label = QLabel("Step 3: Objects & Drawing")

        # Style for active and inactive steps
        self.active_step_style = "color: blue; font-weight: bold;"
        self.inactive_step_style = "color: gray;"

        # Add step labels
        step_layout.addWidget(self.step1_label)
        step_layout.addWidget(self.step2_label)
        step_layout.addWidget(self.step3_label)

        # ----------------------------
        # Terrain Setup
        # ----------------------------
        self.terrain_group = QGroupBox("Terrain Setup")
        terrain_layout = QVBoxLayout()
        self.terrain_group.setLayout(terrain_layout)

        # GeoTIFF Button
        self.load_tiff_button = QPushButton("Load GeoTIFF")
        self.load_tiff_button.setFixedHeight(40)
        terrain_layout.addWidget(self.load_tiff_button)

        # Set Map Topo Button (was PNG Overlay)
        self.set_map_topo_button = QPushButton("Set Map Topo")
        self.set_map_topo_button.setIcon(fugue.icon('map', size=20, fallback_size=True))
        self.set_map_topo_button.setIconSize(QSize(20, 20))
        self.set_map_topo_button.setFixedHeight(36)
        self.set_map_topo_button.setToolTip("Set a topographic map overlay (PNG)")
        self.set_map_topo_button.setEnabled(True)
        terrain_layout.addWidget(self.set_map_topo_button)

        # Set CSRA Button (was Capture CSRA)
        self.set_csra_button = QPushButton("Set CSRA")
        self.set_csra_button.setIcon(fugue.icon('camera-photo', size=20, fallback_size=True))
        self.set_csra_button.setIconSize(QSize(20, 20))
        self.set_csra_button.setFixedHeight(36)
        self.set_csra_button.setToolTip("Set CSRA overlay from second monitor")
        self.set_csra_button.setEnabled(True)
        terrain_layout.addWidget(self.set_csra_button)

        # Overlay status label
        # self.overlay_status_label = QLabel("No overlays available.")
        # terrain_layout.addWidget(self.overlay_status_label)  # TEMP: Commented out for debug

        # Next button
        self.next_button = QPushButton("Next")
        self.next_button.setFixedHeight(36)
        self.next_button.setEnabled(False)
        terrain_layout.addWidget(self.next_button)

        # ----------------------------
        # Overlay Control Group (now only the switch)
        # ----------------------------
        self.overlay_group = QGroupBox("Overlay Control")
        overlay_layout = QVBoxLayout()
        self.overlay_group.setLayout(overlay_layout)
        # Overlay status label (now in overlay group)
        self.overlay_status_label = QLabel("No overlays available.")
        overlay_layout.addWidget(self.overlay_status_label)
        # Overlay switch (Map Topo <-> CSRA)
        overlay_switch_layout = QHBoxLayout()
        overlay_switch_label = QLabel("Overlay:")
        self.overlay_map_radio = QRadioButton("Map Topo")
        self.overlay_map_radio.setIcon(fugue.icon('map', size=16, fallback_size=True))
        self.overlay_map_radio.setIconSize(QSize(16, 16))
        self.overlay_map_radio.setToolTip("Show map topo overlay")
        self.overlay_csra_radio = QRadioButton("CSRA")
        self.overlay_csra_radio.setIcon(fugue.icon('camera-photo', size=16, fallback_size=True))
        self.overlay_csra_radio.setIconSize(QSize(16, 16))
        self.overlay_csra_radio.setToolTip("Show CSRA overlay")
        self.overlay_radio_group = QButtonGroup(self)
        self.overlay_radio_group.addButton(self.overlay_map_radio)
        self.overlay_radio_group.addButton(self.overlay_csra_radio)
        self.overlay_map_radio.setChecked(True)
        self.overlay_map_radio.setEnabled(False)
        self.overlay_csra_radio.setEnabled(False)
        overlay_switch_layout.addWidget(self.overlay_map_radio)
        overlay_switch_layout.addWidget(self.overlay_csra_radio)
        overlay_layout.addLayout(overlay_switch_layout)
        self.overlay_group.setVisible(False)

        # Switch Overlay Button (unchanged, but update text logic below)
        self.switch_overlay_btn = QPushButton("Switch Overlay")
        self.switch_overlay_btn.setIcon(fugue.icon('arrow-switch', size=16, fallback_size=True))
        self.switch_overlay_btn.setIconSize(QSize(16, 16))
        self.switch_overlay_btn.setEnabled(False)
        overlay_layout.addWidget(self.switch_overlay_btn)
        self.switch_overlay_btn.clicked.connect(self.handle_switch_overlay)

        # ----------------------------
        # Step 2: Project Setup
        # ----------------------------
        self.project_group = QGroupBox("Project Setup")
        self.project_group.setVisible(False)  # Initially hidden
        project_layout = QVBoxLayout()
        self.project_group.setLayout(project_layout)

        # Project Setup Button
        self.project_setup_button = QPushButton("Configure Project")
        self.project_setup_button.setFixedHeight(40)
        project_layout.addWidget(self.project_setup_button)

        # ----------------------------
        # Step 3: Objects & Drawing
        # ----------------------------
        self.objects_group = QGroupBox("Objects")
        self.objects_group.setVisible(False)  # Initially hidden
        objects_layout = QVBoxLayout()
        self.objects_group.setLayout(objects_layout)

        # Use the existing PaletteTree
        self.palette_tree = PaletteTree()
        objects_layout.addWidget(self.palette_tree)

        # ----------------------------
        # Animation Control
        # ----------------------------
        self.animation_group = QGroupBox("Animation Control")
        self.animation_group.setVisible(False) # Initially hidden
        animation_layout = QVBoxLayout()

        # Playback controls layout
        playback_layout = QHBoxLayout()
        
        # Play Sequence button with blue styling
        play_sequence_btn = QPushButton("Play Trajectories")
        play_sequence_btn.setStyleSheet("""
            QPushButton {
                background-color: #008CBA;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                height: 40px;
            }
            QPushButton:hover {
                background-color: #007399;
            }
        """)
        playback_layout.addWidget(play_sequence_btn)
        
        # Pause button with orange styling
        pause_sequence_btn = QPushButton("Pause")
        pause_sequence_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                height: 40px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #FFE0B2;
                color: #999;
            }
        """)
        pause_sequence_btn.setEnabled(False)  # Initially disabled
        
        playback_layout.addWidget(pause_sequence_btn)
        
        animation_layout.addLayout(playback_layout)

        # --- Speed Control ---
        speed_control_group = QGroupBox("Playback Speed")
        speed_layout = QVBoxLayout()
        speed_control_group.setLayout(speed_layout)
        
        self.speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.speed_slider.setMinimum(0)
        self.speed_slider.setMaximum(6)
        self.speed_slider.setValue(0)
        self.speed_slider.setTickPosition(QSlider.TicksBelow)
        self.speed_slider.setTickInterval(1)
        speed_layout.addWidget(self.speed_slider)
        
        self.speed_label = QLabel("Current Speed: x1")
        self.speed_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        speed_layout.addWidget(self.speed_label)
        
        animation_layout.addWidget(speed_control_group)
        # ---------------------
        
        self.animation_group.setLayout(animation_layout)

        # --- Portée Control Group ---
        self.portee_group = QGroupBox("Portée Control")
        portee_layout = QVBoxLayout()
        # Main switch with label
        main_switch_layout = QHBoxLayout()
        main_switch_label = QLabel("Visualiser Portée")
        self.visualiser_portee_switch = Switch("ON", "OFF")
        self.visualiser_portee_switch.setChecked(True)
        main_switch_layout.addWidget(main_switch_label)
        main_switch_layout.addWidget(self.visualiser_portee_switch)
        portee_layout.addLayout(main_switch_layout)
        # Individual switches with labels
        self.portee1_switch = Switch("ON", "OFF")
        self.portee2_switch = Switch("ON", "OFF")
        self.portee3_switch = Switch("ON", "OFF")
        self.portee1_switch.setChecked(True)
        self.portee2_switch.setChecked(True)
        self.portee3_switch.setChecked(True)
        self.portee_labels = []
        for i, (label, switch) in enumerate([
            ("AP", self.portee1_switch),
            ("AC", self.portee2_switch),
            ("Missile", self.portee3_switch)
        ]):
            row = QHBoxLayout()
            lbl = QLabel(label)
            self.portee_labels.append(lbl)
            row.addWidget(lbl)
            row.addWidget(switch, 0)  # No stretch for switch
            portee_layout.addLayout(row)
        self.portee_group.setLayout(portee_layout)
        self.portee_group.setVisible(False)  # Ensure hidden by default
        print("[DEBUG] Portée Control panel set to hidden by default in setup_ui")

        # --- Health Bar Control Group ---
        self.health_bar_group = QGroupBox("Health Bar Control")
        health_bar_layout = QVBoxLayout()
        self.health_bar_toggle = QCheckBox("Show Health Bars")
        self.health_bar_toggle.setChecked(True)
        # Connect to MainWindow's toggle_health_bars if possible
        if hasattr(self.main_window, 'toggle_health_bars'):
            self.health_bar_toggle.stateChanged.connect(self.main_window.toggle_health_bars)
        # Placeholder for future controls
        self.health_bar_placeholder = QLabel("More controls coming soon...")
        self.health_bar_placeholder.setStyleSheet("color: gray; font-size: 10px;")
        health_bar_layout.addWidget(self.health_bar_toggle)
        # --- Add Simulation Report Button ---
        self.sim_report_btn = QPushButton("Simulation Report")
        self.sim_report_btn.setEnabled(False)
        self.sim_report_btn.setToolTip("Download fight report as PNG after simulation ends.")
        self.sim_report_btn.clicked.connect(self.handle_sim_report_btn_clicked)
        health_bar_layout.addWidget(self.sim_report_btn)
        health_bar_layout.addWidget(self.health_bar_placeholder)
        self.health_bar_group.setLayout(health_bar_layout)

        # ----------------------------
        # Add all sections to the main layout
        # ----------------------------
        main_layout.addWidget(self.step_group)
        main_layout.addWidget(self.terrain_group)
        main_layout.addWidget(self.project_group)
        main_layout.addWidget(self.overlay_group)  # <--- Add overlay group before objects
        main_layout.addWidget(self.objects_group)
        main_layout.addWidget(self.animation_group)
        main_layout.addWidget(self.portee_group)
        main_layout.addWidget(self.health_bar_group)
        main_layout.addStretch()

        # ----------------------------
        # Connect signals
        # ----------------------------
        main_window = self.main_window
        self.load_tiff_button.clicked.connect(main_window.load_geotiff)
        self.set_map_topo_button.clicked.connect(main_window.load_png_overlay)
        self.set_csra_button.clicked.connect(self.handle_capture_csra)
        self.next_button.clicked.connect(self.handle_next_step)
        self.overlay_map_radio.toggled.connect(lambda checked: self.handle_overlay_radio('MAP_TOPO', checked))
        self.overlay_csra_radio.toggled.connect(lambda checked: self.handle_overlay_radio('CSRA', checked))
        self.project_setup_button.clicked.connect(main_window.show_project_setup_dialog)
        play_sequence_btn.clicked.connect(main_window.play_scene_sequence)
        pause_sequence_btn.clicked.connect(main_window.pause_scene_sequence)
        self.speed_slider.valueChanged.connect(self.main_window.on_speed_slider_changed)
        
        # Connect preview buttons
        # t1_btn.clicked.connect(lambda: main_window.preview_time("T1"))
        # t2_btn.clicked.connect(lambda: main_window.preview_time("T2"))
        # t3_btn.clicked.connect(lambda: main_window.preview_time("T3"))
        # global_view_btn.clicked.connect(main_window.restore_global_view) 

        # Store buttons for state management
        main_window.play_sequence_btn = play_sequence_btn
        main_window.pause_sequence_btn = pause_sequence_btn
        # main_window.preview_buttons = [t1_btn, t2_btn, t3_btn]
        # main_window.global_view_btn = global_view_btn
        print(f"[DEBUG] Assigned pause_sequence_btn to main_window: {main_window.pause_sequence_btn}")

        # Initialize step styles
        self.update_step_styles()

        # --- Portée Control Logic ---
        def update_portee_switches():
            visible = self.visualiser_portee_switch.isChecked()
            main_win = self.main_window
            # print(f"[DEBUG] update_portee_switches: Visualiser Portee is now {'ON' if visible else 'OFF'}")
            # print(f"[DEBUG] update_portee_switches: main_win type={type(main_win)}, repr={repr(main_win)}")
            # Set the global portée flag
            if hasattr(main_win, 'portee_enabled'):
                main_win.portee_enabled = visible
                # print(f"[DEBUG] MainWindow.portee_enabled set to {main_win.portee_enabled}")
            # Set all individual switches to match master (UI and logic)
            self.portee1_switch.setChecked(visible)
            self.portee2_switch.setChecked(visible)
            self.portee3_switch.setChecked(visible)
            main_win.portee1_enabled = visible
            main_win.portee2_enabled = visible
            main_win.portee3_enabled = visible
            print(f"[DEBUG] Individual switches set to: AP={self.portee1_switch.isChecked()}, AC={self.portee2_switch.isChecked()}, Missile={self.portee3_switch.isChecked()}")
            print(f"[DEBUG] Logic flags set to: portee1_enabled={main_win.portee1_enabled}, portee2_enabled={main_win.portee2_enabled}, portee3_enabled={main_win.portee3_enabled}")
            # Show/hide individual portée switches
            for lbl, sw in zip(self.portee_labels, [self.portee1_switch, self.portee2_switch, self.portee3_switch]):
                lbl.setVisible(True)
                sw.setVisible(True)
                sw.setEnabled(True)
            # Update all objects' portée polygons
            if hasattr(main_win, 'live_objects'):
                # print(f"[DEBUG] update_portee_switches: live_objects len={len(main_win.live_objects)} contents={[obj.name for obj in main_win.live_objects]}")
                for obj in main_win.live_objects:
                    # print(f"[DEBUG] update_portee_switches: Updating {obj.name}")
                    obj.set_portee_visible(1, main_win.portee_enabled and main_win.portee1_enabled)
                    obj.set_portee_visible(2, main_win.portee_enabled and main_win.portee2_enabled)
                    obj.set_portee_visible(3, main_win.portee_enabled and main_win.portee3_enabled)
            else:
                # print(f"[DEBUG] update_portee_switches: main_win has no live_objects attribute")
                pass

        def toggle_portee_all(portee_num, checked):
            main_win = self.main_window
            enabled = checked
            # print(f"[DEBUG] toggle_portee_all: CALLED for portee_num={portee_num}, checked={checked}, main_win type={type(main_win)}, repr={repr(main_win)}")
            if portee_num == 1:
                main_win.portee1_enabled = enabled
            elif portee_num == 2:
                main_win.portee2_enabled = enabled
            elif portee_num == 3:
                main_win.portee3_enabled = enabled
            # print(f"[DEBUG] toggle_portee_all: Portee {portee_num} set to {enabled}")
            # Update all objects' portée polygons
            if hasattr(main_win, 'live_objects'):
                # print(f"[DEBUG] toggle_portee_all: live_objects len={len(main_win.live_objects)} contents={[obj.name for obj in main_win.live_objects]}")
                for obj in main_win.live_objects:
                    # print(f"[DEBUG] toggle_portee_all: Updating {obj.name}")
                    obj.set_portee_visible(1, main_win.portee_enabled and main_win.portee1_enabled)
                    obj.set_portee_visible(2, main_win.portee_enabled and main_win.portee2_enabled)
                    obj.set_portee_visible(3, main_win.portee_enabled and main_win.portee3_enabled)
            else:
                # print(f"[DEBUG] toggle_portee_all: main_win has no live_objects attribute")
                pass

        self.visualiser_portee_switch.toggled.connect(update_portee_switches)
        self.portee1_switch.toggled.connect(lambda checked: toggle_portee_all(1, checked))
        self.portee2_switch.toggled.connect(lambda checked: toggle_portee_all(2, checked))
        self.portee3_switch.toggled.connect(lambda checked: toggle_portee_all(3, checked))
        update_portee_switches()

        # --- Overlay radio logic ---
        def update_overlay_radios():
            png_loaded = self.png_loaded
            csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
            print(f"[DEBUG] update_overlay_radios: png_loaded={png_loaded}, csra_captured={csra_captured}")
            self.overlay_map_radio.setEnabled(png_loaded)
            self.overlay_csra_radio.setEnabled(csra_captured)
            if png_loaded and not csra_captured:
                self.overlay_map_radio.setChecked(True)
                self.main_window.set_overlay_type('MAP_TOPO')
            elif csra_captured and not png_loaded:
                self.overlay_csra_radio.setChecked(True)
                self.main_window.set_overlay_type('CSRA')
            elif not png_loaded and not csra_captured:
                self.overlay_map_radio.setEnabled(False)
                self.overlay_csra_radio.setEnabled(False)
            self.update_overlay_status()

        self.overlay_map_radio.toggled.connect(lambda checked: self.handle_overlay_radio('MAP_TOPO', checked))
        self.overlay_csra_radio.toggled.connect(lambda checked: self.handle_overlay_radio('CSRA', checked))

        self.geotiff_loaded = False
        self.update_next_button_state()
        self.update_overlay_status()

    def update_step_styles(self):
        """Updates the visual style of step indicators based on current step."""
        # Reset all steps to inactive
        self.step1_label.setStyleSheet(self.inactive_step_style)
        self.step2_label.setStyleSheet(self.inactive_step_style)
        self.step3_label.setStyleSheet(self.inactive_step_style)

        # Set current step to active
        if self.current_step == 1:
            self.step1_label.setStyleSheet(self.active_step_style)
        elif self.current_step == 2:
            self.step2_label.setStyleSheet(self.active_step_style)
        elif self.current_step == 3:
            self.step3_label.setStyleSheet(self.active_step_style)

    def advance_to_step(self, step):
        """Advances to the specified step and updates UI accordingly."""
        self.current_step = step
        self.update_step_styles()

        # Show/hide appropriate groups
        self.terrain_group.setVisible(step == 1)
        self.project_group.setVisible(step == 2)
        self.objects_group.setVisible(step == 3)

    def on_geotiff_loaded(self):
        self.geotiff_loaded = True
        self.update_next_button_state()
        self.update_overlay_status()

    def on_png_loaded(self):
        self.png_loaded = True
        self.overlay_group.setVisible(True)
        self.overlay_map_radio.setChecked(True)
        self.main_window.set_overlay_type('MAP_TOPO')
        self.update_next_button_state()
        self.update_overlay_status()

    def on_project_setup_complete(self, project_data):
        """Called when project setup is complete."""
        self.advance_to_step(3)
        self.update_palette_for_project(project_data)
        self.update_objects_tree_for_scenario(project_data)
        self.animation_group.setVisible(True)
        self.overlay_group.setVisible(True)  # Ensure overlay group remains visible

    def update_palette_for_project(self, project_data):
        """Updates the palette based on project settings."""
        if not project_data:
            return

        # Show objects group
        self.objects_group.setVisible(True)

        # Clear existing items
        self.palette_tree.clear()

        # Get project parameters
        technical_level = project_data.get('technical_level', '')
        combat_type = project_data.get('combat_type', '')

        # Create appropriate object categories based on project parameters
        if technical_level == 'Brigade':
            self.add_brigade_objects(combat_type)
        elif technical_level == 'GPT':
            self.add_gpt_objects(combat_type)

        # Expand all items
        self.palette_tree.expandAll()

    def add_brigade_objects(self, combat_type):
        """Adds objects specific to Brigade level."""
        # Vehicles category
        vehicles_item = QTreeWidgetItem(["Vehicles"])
        vehicles_icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'vehicles_icon.png')
        vehicles_item.setIcon(0, QIcon(vehicles_icon_path))

        if combat_type == 'Mecanise':
            # Add mechanized vehicles
            self.add_vehicle_item(vehicles_item, "Tank", "tank.png")
            self.add_vehicle_item(vehicles_item, "APC", "apc.png")
            self.add_vehicle_item(vehicles_item, "IFV", "ifv.png")
        else:  # Motorise
            # Add motorized vehicles
            self.add_vehicle_item(vehicles_item, "Truck", "truck.png")
            self.add_vehicle_item(vehicles_item, "Jeep", "jeep.png")
            self.add_vehicle_item(vehicles_item, "Transport", "transport.png")

        self.palette_tree.addTopLevelItem(vehicles_item)

        # Add common elements
        self.add_common_elements()

    def add_gpt_objects(self, combat_type):
        """Adds objects specific to GPT level."""
        # Vehicles category
        vehicles_item = QTreeWidgetItem(["Vehicles"])
        vehicles_icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'vehicles_icon.png')
        vehicles_item.setIcon(0, QIcon(vehicles_icon_path))

        if combat_type == 'Mecanise':
            # Add mechanized vehicles
            self.add_vehicle_item(vehicles_item, "Tank", "tank.png")
            self.add_vehicle_item(vehicles_item, "APC", "apc.png")
        else:  # Motorise
            # Add motorized vehicles
            self.add_vehicle_item(vehicles_item, "Truck", "truck.png")
            self.add_vehicle_item(vehicles_item, "Jeep", "jeep.png")

        self.palette_tree.addTopLevelItem(vehicles_item)

        # Add common elements
        self.add_common_elements()

    def add_common_elements(self):
        """Adds common elements for both levels."""
        # Civil Elements category
        civils_item = QTreeWidgetItem(["Civil Elements"])
        civil_icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'civil_icon.png')
        civils_item.setIcon(0, QIcon(civil_icon_path))

        self.add_vehicle_item(civils_item, "House", "house.png")
        self.add_vehicle_item(civils_item, "Tree", "tree.png")
        self.add_vehicle_item(civils_item, "Building", "building.png")

        self.palette_tree.addTopLevelItem(civils_item)

        # Characters category
        chars_item = QTreeWidgetItem(["Characters"])
        characters_icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'characters_icon.png')
        chars_item.setIcon(0, QIcon(characters_icon_path))

        self.add_vehicle_item(chars_item, "Person", "person.png")
        self.add_vehicle_item(chars_item, "Soldier", "soldier.png")

        self.palette_tree.addTopLevelItem(chars_item)

    def add_vehicle_item(self, parent, name, icon_name):
        """Helper method to add a vehicle item to the tree."""
        item = QTreeWidgetItem([name])
        icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', icon_name)
        item.setIcon(0, QIcon(icon_path))
        item.setData(0, Qt.UserRole, name)
        parent.addChild(item)

    def set_distance(self, text):
        # self.distance_label.setText(text)
        pass

    def update_objects_tree_for_scenario(self, project_data):
        from objects import gpt_moto, gpt_meca, S_gpt_moto, S_gpt_meca
        technical_level = project_data.get('technical_level', '').lower()
        combat_type = project_data.get('combat_type', '').lower()
        if technical_level == 'brigade' and combat_type == 'motorise':
            scenario_obj = gpt_moto()
        elif technical_level == 'brigade' and combat_type == 'mecanise':
            scenario_obj = gpt_meca()
        elif technical_level == 'gpt' and combat_type == 'motorise':
            scenario_obj = S_gpt_moto()
        elif technical_level == 'gpt' and combat_type == 'mecanise':
            scenario_obj = S_gpt_meca()
        else:
            scenario_obj = None
        self.palette_tree.populate_tree(scenario_obj)
        self.objects_group.setVisible(True)

    def toggle_portee(self, portee_num, visible):
        # Get the selected object (implement this as needed)
        main_win = self.main_window
        if hasattr(main_win, 'view') and hasattr(main_win.view, 'scene'):
            selected_items = main_win.view.scene.selectedItems()
            if selected_items:
                item = selected_items[0]
                obj = main_win.find_object_by_graphic(item)
                if obj:
                    if portee_num == 1 and hasattr(obj, 'los_polygon_item') and obj.los_polygon_item:
                        obj.los_polygon_item.setVisible(visible)
                    elif portee_num == 2 and hasattr(obj, 'portee2_los_polygon_item') and obj.portee2_los_polygon_item:
                        obj.portee2_los_polygon_item.setVisible(visible)
                    elif portee_num == 3 and hasattr(obj, 'portee3_los_polygon_item') and obj.portee3_los_polygon_item:
                        obj.portee3_los_polygon_item.setVisible(visible)

    def handle_capture_csra(self):
        success = self.main_window.capture_csra_overlay()
        if success:
            self._csra_captured = True
            self.set_csra_button.setText("Re-take CSRA")
            self.overlay_csra_radio.setChecked(True)
            self.main_window.set_overlay_type('CSRA')
            self.update_next_button_state()
            self.update_overlay_status()
            print("CSRA screenshot captured (real, second monitor)")

    def update_overlay_status(self):
        png_loaded = self.png_loaded
        csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
        status = []
        if png_loaded:
            status.append("MAP_TOPO overlay loaded" + (" (active)" if self.overlay_map_radio.isChecked() else ""))
        if csra_captured:
            status.append("CSRA captured" + (" (active)" if self.overlay_csra_radio.isChecked() else ""))
        if not status:
            self.overlay_status_label.setText("No overlays available.")
        else:
            self.overlay_status_label.setText(" / ".join(status))
        print(f"[DEBUG] update_overlay_status: {self.overlay_status_label.text()}")
        self.overlay_map_radio.setEnabled(png_loaded)
        self.overlay_csra_radio.setEnabled(csra_captured)
        if png_loaded and not csra_captured:
            self.overlay_map_radio.setChecked(True)
        elif csra_captured and not png_loaded:
            self.overlay_csra_radio.setChecked(True)
        elif not png_loaded and not csra_captured:
            self.overlay_map_radio.setChecked(False)
            self.overlay_csra_radio.setChecked(False)
        self.update_switch_overlay_btn()

    def update_next_button_state(self):
        png_loaded = self.png_loaded
        csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
        print(f"[DEBUG] update_next_button_state: geotiff_loaded={self.geotiff_loaded}, png_loaded={png_loaded}, csra_captured={csra_captured}")
        self.next_button.setEnabled(self.geotiff_loaded and (png_loaded or csra_captured))

    def handle_next_step(self):
        self.advance_to_step(2)
        self.project_group.setVisible(True)
        self.overlay_group.setVisible(True)

    def handle_overlay_radio(self, overlay_type, checked):
        if not checked:
            return
        png_loaded = self.png_loaded
        csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
        print(f"[DEBUG] handle_overlay_radio: overlay_type={overlay_type}, checked={checked}, png_loaded={png_loaded}, csra_captured={csra_captured}")
        if overlay_type == 'MAP_TOPO':
            if png_loaded:
                self.main_window.set_overlay_type('MAP_TOPO')
            else:
                QMessageBox.warning(self, "MAP_TOPO Not Available", "No MAP_TOPO overlay is available. Please load a MAP_TOPO overlay first or use CSRA.")
                if csra_captured:
                    self.overlay_csra_radio.setChecked(True)
                else:
                    self.overlay_map_radio.setChecked(False)
        elif overlay_type == 'CSRA':
            if csra_captured:
                self.main_window.set_overlay_type('CSRA')
            else:
                QMessageBox.warning(self, "CSRA Not Available", "No CSRA capture is available. Please capture CSRA first or use MAP_TOPO overlay.")
                if png_loaded:
                    self.overlay_map_radio.setChecked(True)
                else:
                    self.overlay_csra_radio.setChecked(False)
        self.update_overlay_status()

    # --- Add the handler method to ComprehensivePalette ---
    def handle_switch_overlay(self):
        png_loaded = self.png_loaded
        csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
        if png_loaded and csra_captured:
            # Toggle overlay
            if self.overlay_map_radio.isChecked():
                self.overlay_csra_radio.setChecked(True)
                self.main_window.set_overlay_type('CSRA')
            else:
                self.overlay_map_radio.setChecked(True)
                self.main_window.set_overlay_type('MAP_TOPO')
            self.update_overlay_status()
            self.update_switch_overlay_btn()

    # --- Add a helper to update the button state and text ---
    def update_switch_overlay_btn(self):
        png_loaded = self.png_loaded
        csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
        if png_loaded and csra_captured:
            if self.overlay_map_radio.isChecked():
                self.switch_overlay_btn.setText("Switch to CSRA")
            else:
                self.switch_overlay_btn.setText("Switch to MAP_TOPO")
            self.switch_overlay_btn.setEnabled(True)
        else:
            self.switch_overlay_btn.setText("Switch Overlay")
            self.switch_overlay_btn.setEnabled(False)

    def handle_sim_report_btn_clicked(self):
        if hasattr(self.main_window, 'generate_simulation_report'):
            self.main_window.generate_simulation_report()

# ---------------------------------------------------
# Subclass QTreeWidget to customize drag behavior
# ---------------------------------------------------
class PaletteTree(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setHeaderHidden(True)
        self.setDragEnabled(True)
        self.populate_tree()

    def populate_tree(self, scenario_obj=None):
        """Populate the tree with items."""
        from objects import (
            S_gpt_meca,
              S_gpt_moto,
                gpt_meca, gpt_moto, enmi_brigade, enmi_company, enmi_section,
            Bataillon_meca, Bataillon_moto, Brigade_meca, Brigade_moto, Compagnie_meca, Compagnie_moto,
            Groupement_meca, Groupement_moto, Section_meca, Section_moto, Sous_groupement_meca, Sous_groupement_moto,
            Bataillon_meca_Ennemi, Bataillon_moto_Ennemi, Brigade_meca_Ennemi, Brigade_moto_Ennemi, Compagnie_meca_Ennemi,
            Compagnie_moto_Ennemi, Groupement_meca_Ennemi, Groupement_moto_Ennemi, Section_meca_Ennemi, Section_moto_Ennemi,
            Sous_groupement_meca_Ennemi, Sous_groupement_moto_Ennemi,
            Cas, Cml, Esc, Geb, Reco, Sac,
            Jeep_ami, Tank_ami, Jeep_ennemi, Tank_ennemi, M113_ami, M113_ennemi
        )
        self.clear()
        
        # Always add Unités and Ennemi as top-level groups
        unitees_item = QTreeWidgetItem(self)
        unitees_item.setText(0, "Unités")
        unitees_item.setExpanded(True)
        ennemi_item = QTreeWidgetItem(self)
        ennemi_item.setText(0, "Ennemi")
        ennemi_item.setExpanded(True)
        
        # List of Unités unit classes
        unitees_classes = [
                Brigade_meca,
                  Brigade_moto,
                   Groupement_meca,
                  Groupement_moto,
                Bataillon_moto,
                Bataillon_meca,
                Sous_groupement_meca, 
                Sous_groupement_moto,
                Compagnie_moto,
                Compagnie_meca,
                Section_meca,
                    Section_moto,
            Jeep_ami,
            Tank_ami,
            M113_ami,
            Cas, 
            Cml,
              Esc,
                Geb,
                  Reco,
                    Sac
        ]
        for cls in unitees_classes:
            obj = cls()
            print(f"[DEBUG] Adding Ami unit: {obj.designation} (name: {obj.name}, image: {obj.image})")
            item = QTreeWidgetItem(unitees_item)
            item.setText(0, obj.designation)
            # Set icon
            if os.path.exists(obj.image):
                print(f"[DEBUG] Image found for {obj.name}: {obj.image}")
                pixmap = QPixmap(obj.image)
                scaled_pixmap = pixmap.scaled(QSize(32, 32), Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon = QIcon(scaled_pixmap)
            else:
                print(f"[DEBUG] Image NOT found for {obj.name}, using fallback icon.")
                pixmap = QPixmap(32, 32)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                painter.setPen(QPen(QColor(0, 0, 0)))
                painter.setBrush(QBrush(QColor(0, 0, 255)))
                painter.drawRect(2, 2, 28, 28)
                painter.end()
                icon = QIcon(pixmap)
            item.setIcon(0, icon)
            item.setData(0, Qt.UserRole, obj.name)
            print(f"[DEBUG] Ami unit added to palette: {obj.name}")

        # List of Ennemi unit classes
        ennemi_classes = [
                Brigade_meca_Ennemi,
                  Brigade_moto_Ennemi,
              Groupement_meca_Ennemi,
                Groupement_moto_Ennemi,
            Bataillon_meca_Ennemi,
              Bataillon_moto_Ennemi,
                    Compagnie_meca_Ennemi,
            Compagnie_moto_Ennemi,
            Sous_groupement_meca_Ennemi,
              Sous_groupement_moto_Ennemi,
                  Section_meca_Ennemi,
                    Section_moto_Ennemi,
            Jeep_ennemi,
            Tank_ennemi,
            M113_ennemi
        ]
        for cls in ennemi_classes:
            obj = cls()
            item = QTreeWidgetItem(ennemi_item)
            item.setText(0, obj.designation)
            # Set icon
            if os.path.exists(obj.image):
                pixmap = QPixmap(obj.image)
                scaled_pixmap = pixmap.scaled(QSize(32, 32), Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon = QIcon(scaled_pixmap)
            else:
                pixmap = QPixmap(32, 32)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                painter.setPen(QPen(QColor(0, 0, 0)))
                painter.setBrush(QBrush(QColor(255, 0, 0)))
                painter.drawRect(2, 2, 28, 28)
                painter.end()
                icon = QIcon(pixmap)
            item.setIcon(0, icon)
            item.setData(0, Qt.UserRole, cls.__name__)
        # Expand all items
        self.expandAll()

    def startDrag(self, actions):
        item = self.currentItem()
        if not item:
            return
        object_type = item.data(0, Qt.UserRole)
        if not object_type:
            return
        drag = QDrag(self)
        mime_data = QMimeData()
        mime_data.setText(object_type)
        drag.setMimeData(mime_data)
        icon = item.icon(0)
        if not icon.isNull():
            pixmap = icon.pixmap(32, 32)
            drag.setPixmap(pixmap)
        else:
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)
            drag.setPixmap(pixmap)
        drag.exec_(actions)

# ---------------------------------------------------
# Animation Trigger System (PowerPoint-style)
# ---------------------------------------------------
class AnimationTrigger(Enum):
    """PowerPoint-style animation triggers"""
    CLICK = "Click"                    # Start on mouse click
    WITH_PREVIOUS = "With Previous"    # Start at same time as previous
    AFTER_PREVIOUS = "After Previous"  # Start after previous finishes
    ON_TIME = "On Time"               # Start after specified delay

class AnimationProperties:
    """Enhanced animation properties with PowerPoint-style controls"""
    def __init__(self, unit_name, trajectory_points):
        self.unit_name = unit_name
        self.trajectory_points = trajectory_points
        self.trigger = AnimationTrigger.CLICK
        self.delay = 0.0          # Delay in seconds
        self.duration = 5.0       # Duration in seconds (Auto = calculated from trajectory)
        self.order = 0            # Sequence order
        self.repeat = 1           # Number of repeats
        self.easing = "Linear"    # Easing curve type
        self.auto_reverse = False # Auto reverse animation
        self.enabled = True       # Animation enabled/disabled

    def get_trigger_icon(self):
        """Get icon for trigger type"""
        icons = {
            AnimationTrigger.CLICK: "🖱️",
            AnimationTrigger.WITH_PREVIOUS: "⏯️",
            AnimationTrigger.AFTER_PREVIOUS: "⏭️",
            AnimationTrigger.ON_TIME: "⏰"
        }
        return icons.get(self.trigger, "🎯")

    def get_trigger_description(self):
        """Get description for trigger type"""
        descriptions = {
            AnimationTrigger.CLICK: "Start on click",
            AnimationTrigger.WITH_PREVIOUS: "Start with previous",
            AnimationTrigger.AFTER_PREVIOUS: "Start after previous",
            AnimationTrigger.ON_TIME: f"Start after {self.delay}s"
        }
        return descriptions.get(self.trigger, "Unknown trigger")

# ---------------------------------------------------
# Animation Settings Dialog (PowerPoint-style)
# ---------------------------------------------------
class AnimationSettingsDialog(QDialog):
    """PowerPoint-style animation settings dialog"""

    def __init__(self, anim_props, parent=None):
        super().__init__(parent)
        self.anim_props = anim_props
        self.setup_ui()

    def setup_ui(self):
        """Setup the settings dialog UI"""
        self.setWindowTitle(f"Animation Settings - {self.anim_props.unit_name}")
        self.setFixedSize(400, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel(f"⚙️ Animation Settings")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # Unit info
        unit_info = QLabel(f"🎯 Unit: {self.anim_props.unit_name}")
        unit_info.setStyleSheet("font-weight: bold; color: #34495e; margin-bottom: 10px;")
        layout.addWidget(unit_info)

        # Trigger settings group
        trigger_group = QGroupBox("🖱️ Trigger Settings")
        trigger_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        trigger_layout = QGridLayout(trigger_group)

        # Trigger type
        trigger_layout.addWidget(QLabel("Trigger Type:"), 0, 0)
        self.trigger_combo = QComboBox()
        for trigger in AnimationTrigger:
            self.trigger_combo.addItem(f"{trigger.value}", trigger)
        current_index = list(AnimationTrigger).index(self.anim_props.trigger)
        self.trigger_combo.setCurrentIndex(current_index)
        trigger_layout.addWidget(self.trigger_combo, 0, 1)

        # Delay
        trigger_layout.addWidget(QLabel("Delay (seconds):"), 1, 0)
        self.delay_spin = QDoubleSpinBox()
        self.delay_spin.setRange(0.0, 60.0)
        self.delay_spin.setSingleStep(0.1)
        self.delay_spin.setValue(self.anim_props.delay)
        trigger_layout.addWidget(self.delay_spin, 1, 1)

        layout.addWidget(trigger_group)

        # Timing settings group
        timing_group = QGroupBox("⏱️ Timing Settings")
        timing_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        timing_layout = QGridLayout(timing_group)

        # Duration
        timing_layout.addWidget(QLabel("Duration (seconds):"), 0, 0)
        self.duration_spin = QDoubleSpinBox()
        self.duration_spin.setRange(0.1, 60.0)
        self.duration_spin.setSingleStep(0.1)
        self.duration_spin.setValue(self.anim_props.duration)
        timing_layout.addWidget(self.duration_spin, 0, 1)

        # Repeat count
        timing_layout.addWidget(QLabel("Repeat Count:"), 1, 0)
        self.repeat_spin = QSpinBox()
        self.repeat_spin.setRange(1, 10)
        self.repeat_spin.setValue(self.anim_props.repeat)
        timing_layout.addWidget(self.repeat_spin, 1, 1)

        # Auto reverse
        self.auto_reverse_check = QCheckBox("Auto Reverse")
        self.auto_reverse_check.setChecked(self.anim_props.auto_reverse)
        timing_layout.addWidget(self.auto_reverse_check, 2, 0, 1, 2)

        layout.addWidget(timing_group)

        # Effect settings group
        effect_group = QGroupBox("🎨 Effect Settings")
        effect_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        effect_layout = QGridLayout(effect_group)

        # Easing curve
        effect_layout.addWidget(QLabel("Easing:"), 0, 0)
        self.easing_combo = QComboBox()
        easing_options = ["Linear", "InQuad", "OutQuad", "InOutQuad", "InCubic", "OutCubic", "InOutCubic"]
        self.easing_combo.addItems(easing_options)
        if self.anim_props.easing in easing_options:
            self.easing_combo.setCurrentText(self.anim_props.easing)
        effect_layout.addWidget(self.easing_combo, 0, 1)

        # Enabled checkbox
        self.enabled_check = QCheckBox("Animation Enabled")
        self.enabled_check.setChecked(self.anim_props.enabled)
        effect_layout.addWidget(self.enabled_check, 1, 0, 1, 2)

        layout.addWidget(effect_group)

        # Buttons
        button_layout = QHBoxLayout()

        # Reset button
        reset_btn = QPushButton("🔄 Reset")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(reset_btn)

        button_layout.addStretch()

        # Cancel button
        cancel_btn = QPushButton("❌ Cancel")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        # Save button
        save_btn = QPushButton("💾 Save")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)

    def reset_settings(self):
        """Reset settings to defaults"""
        self.trigger_combo.setCurrentIndex(0)  # Click
        self.delay_spin.setValue(0.0)
        self.duration_spin.setValue(5.0)
        self.repeat_spin.setValue(1)
        self.auto_reverse_check.setChecked(False)
        self.easing_combo.setCurrentText("Linear")
        self.enabled_check.setChecked(True)

    def save_settings(self):
        """Save settings to animation properties"""
        trigger_index = self.trigger_combo.currentIndex()
        self.anim_props.trigger = list(AnimationTrigger)[trigger_index]
        self.anim_props.delay = self.delay_spin.value()
        self.anim_props.duration = self.duration_spin.value()
        self.anim_props.repeat = self.repeat_spin.value()
        self.anim_props.auto_reverse = self.auto_reverse_check.isChecked()
        self.anim_props.easing = self.easing_combo.currentText()
        self.anim_props.enabled = self.enabled_check.isChecked()

        print(f"🎬 Saved settings for {self.anim_props.unit_name}:")
        print(f"   Trigger: {self.anim_props.trigger.value}")
        print(f"   Duration: {self.anim_props.duration}s")
        print(f"   Delay: {self.anim_props.delay}s")

        self.accept()

# ---------------------------------------------------
# Animation Panel Class (PowerPoint-style)
# ---------------------------------------------------
class AnimationPanel(QWidget):
    """PowerPoint-style Animation Panel for managing unit trajectories and triggers"""

    # Signals
    animation_selected = pyqtSignal(str)  # unit_name
    animation_triggered = pyqtSignal(str)  # unit_name
    preview_requested = pyqtSignal(str)   # unit_name

    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.animation_properties = {}  # Store AnimationProperties objects
        self.selected_animation = None  # Currently selected animation
        self.setup_ui()

    def setup_ui(self):
        """Setup the Animation Panel UI"""
        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("🎬 Animation Panel")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)

        # Animation List with drag-and-drop
        self.animation_list = QListWidget()
        self.animation_list.setDragDropMode(QListWidget.InternalMove)
        self.animation_list.setDefaultDropAction(Qt.MoveAction)
        self.animation_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                padding: 4px;
            }
            QListWidget::item {
                background-color: transparent;
                border: none;
                padding: 2px;
                margin: 2px 0px;
            }
            QListWidget::item:hover {
                background-color: transparent;
            }
            QListWidget::item:selected {
                background-color: transparent;
            }
        """)
        self.animation_list.itemClicked.connect(self.on_animation_selected)
        self.animation_list.model().rowsMoved.connect(self.on_animations_reordered)
        layout.addWidget(self.animation_list)

        # Control Buttons
        controls_layout = QHBoxLayout()

        # Play All button
        self.play_all_btn = QPushButton("▶ Play All")
        self.play_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.play_all_btn.clicked.connect(self.play_all_animations)
        controls_layout.addWidget(self.play_all_btn)

        # Preview button
        self.preview_btn = QPushButton("👁 Preview")
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.preview_btn.clicked.connect(self.preview_selected_animation)
        self.preview_btn.setEnabled(False)
        controls_layout.addWidget(self.preview_btn)

        layout.addLayout(controls_layout)

        # Status label
        self.status_label = QLabel("No animations available")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-style: italic;
                padding: 4px;
            }
        """)
        layout.addWidget(self.status_label)

    def refresh_animation_list(self):
        """Refresh the animation list with current trajectories"""
        self.animation_list.clear()

        if not hasattr(self.main_window, 'unit_trajectories'):
            self.status_label.setText("No animations available")
            return

        trajectories = self.main_window.unit_trajectories
        if not trajectories:
            self.status_label.setText("No animations available")
            return

        # Create or update AnimationProperties for each trajectory
        for unit_name, trajectory_points in trajectories.items():
            if unit_name not in self.animation_properties:
                # Create new animation properties
                self.animation_properties[unit_name] = AnimationProperties(unit_name, trajectory_points)
                # Set order based on creation sequence
                self.animation_properties[unit_name].order = len(self.animation_properties) - 1
            else:
                # Update existing properties with new trajectory points
                self.animation_properties[unit_name].trajectory_points = trajectory_points

        # Remove properties for trajectories that no longer exist
        to_remove = [name for name in self.animation_properties.keys() if name not in trajectories]
        for name in to_remove:
            del self.animation_properties[name]

        # Sort animations by order
        sorted_animations = sorted(self.animation_properties.values(), key=lambda x: x.order)

        # Add each animation as an item
        for anim_props in sorted_animations:
            item = QListWidgetItem()

            # Create animation item widget
            item_widget = self.create_animation_item(anim_props)
            item.setSizeHint(item_widget.sizeHint())

            # Store animation properties in item data
            item.setData(Qt.UserRole, anim_props.unit_name)

            self.animation_list.addItem(item)
            self.animation_list.setItemWidget(item, item_widget)

        self.status_label.setText(f"{len(self.animation_properties)} animation(s) ready")
        self.play_all_btn.setEnabled(len(self.animation_properties) > 0)

    def create_animation_item(self, anim_props):
        """Create a widget for an animation item with PowerPoint-style controls"""
        widget = QWidget()
        widget.setFixedHeight(80)  # Fixed height to prevent overlapping
        widget.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                margin: 2px;
            }
            QWidget:hover {
                background-color: #f8f9fa;
                border-color: #3498db;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)

        # Top row: Order number, name, and action buttons
        top_row = QHBoxLayout()
        top_row.setSpacing(8)

        # Drag handle and order number
        drag_handle = QLabel("⋮⋮")
        drag_handle.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 12px;
                font-weight: bold;
                padding: 2px;
            }
        """)
        drag_handle.setFixedWidth(20)
        top_row.addWidget(drag_handle)

        # Unit name with order number
        name_label = QLabel(f"{anim_props.order + 1}. 🎯 {anim_props.unit_name}")
        name_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #2c3e50;
                font-size: 13px;
                padding: 2px;
            }
        """)
        top_row.addWidget(name_label)

        top_row.addStretch()

        # Quick action buttons
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(4)

        # Settings button
        settings_btn = QPushButton("⚙️")
        settings_btn.setFixedSize(24, 24)
        settings_btn.setToolTip("Animation settings")
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
                transform: scale(1.1);
            }
        """)
        settings_btn.clicked.connect(lambda: self.show_animation_settings(anim_props.unit_name))
        actions_layout.addWidget(settings_btn)

        # Preview button
        preview_btn = QPushButton("👁")
        preview_btn.setFixedSize(24, 24)
        preview_btn.setToolTip("Preview this animation")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: scale(1.1);
            }
        """)
        preview_btn.clicked.connect(lambda: self.preview_animation(anim_props.unit_name))
        actions_layout.addWidget(preview_btn)

        # Play button
        play_btn = QPushButton("▶")
        play_btn.setFixedSize(24, 24)
        play_btn.setToolTip("Play this animation")
        play_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: scale(1.1);
            }
        """)
        play_btn.clicked.connect(lambda: self.play_single_animation(anim_props.unit_name))
        actions_layout.addWidget(play_btn)

        top_row.addLayout(actions_layout)
        layout.addLayout(top_row)

        # Bottom row: Details and trigger controls
        bottom_row = QHBoxLayout()
        bottom_row.setSpacing(8)

        # Trajectory details
        details_label = QLabel(f"📍 {len(anim_props.trajectory_points)} points • ⏱️ {anim_props.duration:.1f}s")
        details_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                padding: 2px;
            }
        """)
        bottom_row.addWidget(details_label)

        bottom_row.addStretch()

        # Trigger controls
        trigger_layout = QHBoxLayout()
        trigger_layout.setSpacing(4)

        # Trigger selector
        trigger_combo = QComboBox()
        trigger_combo.setFixedWidth(140)
        trigger_combo.setFixedHeight(24)
        trigger_combo.setStyleSheet("""
            QComboBox {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 2px 8px;
                font-size: 11px;
                color: #2c3e50;
            }
            QComboBox:hover {
                background-color: #d5dbdb;
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 4px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
            }
        """)

        # Add trigger options with icons
        for trigger in AnimationTrigger:
            icon = {
                AnimationTrigger.CLICK: "🖱️",
                AnimationTrigger.WITH_PREVIOUS: "⏯️",
                AnimationTrigger.AFTER_PREVIOUS: "⏭️",
                AnimationTrigger.ON_TIME: "⏰"
            }.get(trigger, "🎯")
            trigger_combo.addItem(f"{icon} {trigger.value}", trigger)

        # Set current trigger
        current_index = list(AnimationTrigger).index(anim_props.trigger)
        trigger_combo.setCurrentIndex(current_index)
        trigger_combo.currentIndexChanged.connect(
            lambda idx, unit=anim_props.unit_name: self.on_trigger_changed(unit, idx)
        )

        trigger_layout.addWidget(trigger_combo)
        bottom_row.addLayout(trigger_layout)

        layout.addLayout(bottom_row)

        # Store references for updates
        widget.trigger_combo = trigger_combo
        widget.anim_props = anim_props
        widget.name_label = name_label
        widget.details_label = details_label

        return widget

    def on_animation_selected(self, item):
        """Handle animation selection"""
        # Get the unit name from the item data
        unit_name = item.data(Qt.UserRole)
        if unit_name and unit_name in self.animation_properties:
            self.selected_animation = unit_name
            self.animation_selected.emit(unit_name)
            self.preview_btn.setEnabled(True)
            print(f"🎬 Selected animation: {unit_name}")

    def on_animations_reordered(self, parent, start, end, destination, row):
        """Handle animation reordering via drag and drop"""
        print(f"🎬 Animation reordered: moved from {start} to {row}")

        # Update the order in animation_properties
        animations = list(self.animation_properties.values())
        animations.sort(key=lambda x: x.order)

        # Move the animation from old position to new position
        if start < len(animations) and row <= len(animations):
            moved_anim = animations.pop(start)
            insert_pos = row if row < start else row - 1
            animations.insert(insert_pos, moved_anim)

            # Update order numbers
            for i, anim in enumerate(animations):
                anim.order = i

            print(f"🎬 Updated animation order:")
            for anim in animations:
                print(f"   {anim.order + 1}. {anim.unit_name}")

            # Refresh the display
            self.refresh_animation_list()

    def on_trigger_changed(self, unit_name, trigger_index):
        """Handle trigger type change"""
        if unit_name in self.animation_properties:
            trigger = list(AnimationTrigger)[trigger_index]
            self.animation_properties[unit_name].trigger = trigger
            print(f"🎬 Changed trigger for {unit_name} to {trigger.value}")

            # Update the trigger status label
            self.update_animation_item_display(unit_name)

    def update_animation_item_display(self, unit_name):
        """Update the display of a specific animation item"""
        for i in range(self.animation_list.count()):
            item = self.animation_list.item(i)
            if item.data(Qt.UserRole) == unit_name:
                widget = self.animation_list.itemWidget(item)
                if hasattr(widget, 'trigger_status') and hasattr(widget, 'anim_props'):
                    anim_props = self.animation_properties[unit_name]
                    widget.trigger_status.setText(f"{anim_props.get_trigger_icon()} {anim_props.get_trigger_description()}")
                break

    def show_animation_settings(self, unit_name):
        """Show detailed animation settings dialog"""
        if unit_name not in self.animation_properties:
            return

        anim_props = self.animation_properties[unit_name]
        print(f"🎬 Opening settings for {unit_name}")

        # Create settings dialog
        dialog = AnimationSettingsDialog(anim_props, self)
        if dialog.exec_() == QDialog.Accepted:
            # Settings were saved, refresh the display
            self.update_animation_item_display(unit_name)
            print(f"🎬 Settings saved for {unit_name}")

    def play_all_animations(self):
        """Play all animations with PowerPoint-style sequencing"""
        if not self.animation_properties:
            print("❌ No animations to play")
            return

        print("🎬 Starting PowerPoint-style animation sequence...")
        self.main_window.play_powerpoint_sequence(self.animation_properties)

    def preview_selected_animation(self):
        """Preview the currently selected animation"""
        current_item = self.animation_list.currentItem()
        if current_item:
            item_widget = self.animation_list.itemWidget(current_item)
            if item_widget:
                name_label = item_widget.findChild(QLabel)
                if name_label:
                    unit_name = name_label.text().replace("🎯 ", "")
                    self.preview_animation(unit_name)

    def preview_animation(self, unit_name):
        """Preview a specific animation"""
        print(f"🎬 Previewing animation for {unit_name}")
        self.preview_requested.emit(unit_name)

    def play_single_animation(self, unit_name):
        """Play a single animation"""
        print(f"🎬 Playing single animation for {unit_name}")
        # Create temporary trajectory dict with just this unit
        if hasattr(self.main_window, 'unit_trajectories') and unit_name in self.main_window.unit_trajectories:
            # Temporarily store all trajectories
            all_trajectories = self.main_window.unit_trajectories.copy()
            # Set only the selected trajectory
            self.main_window.unit_trajectories = {unit_name: all_trajectories[unit_name]}
            # Play the sequence
            self.main_window.play_scene_sequence()
            # Restore all trajectories
            self.main_window.unit_trajectories = all_trajectories

    def preview_animation(self, unit_name):
        """Preview an animation (show trajectory info)"""
        if unit_name in self.animation_properties:
            anim_props = self.animation_properties[unit_name]
            print(f"👁 Previewing animation: {unit_name}")
            # Show animation details
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "Animation Preview",
                f"🎯 Animation: {unit_name}\n\n"
                f"📍 Trajectory Points: {len(anim_props.trajectory_points)}\n"
                f"⏱️ Duration: {anim_props.duration}s\n"
                f"🎬 Trigger: {anim_props.trigger.value}\n"
                f"⏰ Delay: {anim_props.delay}s\n"
                f"🔄 Repeat: {anim_props.repeat} times\n"
                f"🎨 Easing: {anim_props.easing}\n"
                f"✅ Enabled: {'Yes' if anim_props.enabled else 'No'}"
            )

# ---------------------------------------------------
# Main Window Class
# ---------------------------------------------------
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyMunk with PyQt5 Simulation")
        self.setGeometry(100, 100, 1200, 800)
        # Add F11 shortcut for fullscreen
        QShortcut(QKeySequence("F11"), self, self.toggle_geotiff_fullscreen)

        # Initialize scene management
        self.scenes = []  # List to store saved scenes
        self.current_scene_index = -1
        self.animation_in_progress = False
        self.animation_paused = False
        self.current_animation_group = None
        self.initial_positions = {}  # Store initial positions of objects
        self.speed_multiplier = 1.0 # For playback speed control
        self.sequence_is_playing = False # Flag for full sequence playback

        # NEW CLEAN TRAJECTORY SYSTEM
        self.unit_trajectories = {}  # Clean trajectory storage: {unit_name: [QPointF list]}

        # POWERPOINT-STYLE ANIMATION SYSTEM
        self.unit_animations = {}  # Enhanced animation storage with triggers and properties

        # Initialize coordinate labels
        self.coords_label = QLabel("Current Position: Not set")
        self.coords_label.setStyleSheet("color: blue;")
        self.distance_label = QLabel("Distance: 0.0 meters")
        self.distance_label.setStyleSheet("color: green;")

        self.background_item = None

        # Initialize Physics
        self.physics_space = PhysicsSpace()

        # Initialize Animation Manager
        self.animation_manager = AnimationManager(self.physics_space)

        # --- Initialize GeoTIFF related variables ---
        self.geotiff_ds = None              # GDAL dataset for TIFF
        self.geotiff_geotransform = None    # Affine transform parameters for TIFF
        self.geotiff_inv_geotransform = None # Inverse affine transform for TIFF
        self.geotiff_image_array = None     # Elevation data array (from TIFF band 1)
        self.geotiff_points = []            # Stores selected points (lon, lat, z)
        self.geotiff_tiff_pixmap_item = None # QGraphicsPixmapItem for TIFF display
        self.geotiff_png_pixmap_item = None  # QGraphicsPixmapItem for PNG display
        self.geotiff_png_transform = None   # Geotransform calculated for PNG
        self.active_background_type = 'default' # 'default', 'captured', or 'geotiff'
        # --- End GeoTIFF variables ---

        # Fullscreen state flag
        self.is_view_fullscreen = False

        # Initialize Simulation Objects List
        self.objects = []
        self.live_objects = []  # Track live objects for the live palette
        self.info_labels = {}  # Track info labels for each object

        # Store project data
        self.project_data = {}

        # Setup the UI elements
        self.setup_ui()
        print("[DEBUG][INIT] MainWindow initialized")
        self.update_simulation()  # TEMP: Should print debug lines at startup

        # Add static floor (after UI setup, as it might depend on scene/view)
        self.physics_space.add_floor()

        # Timer for simulation updates (60 FPS)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_simulation)
        print("[DEBUG][TIMER] Timer connected to update_simulation")
        self.timer.start(7888000)  # Call update_simulation every 1 second for debugging
        print("[DEBUG][TIMER] Timer manually started for debug")
        # self.timer.start(16) # Start timer based on mode (e.g., in play_simulation)
        self.simulation_running = False  # Start in Editing mode

        # Set up collision handlers
        self.physics_space.setup_collision_handlers()

        # If there's a default background image, load it (after UI setup)
        self.default_background_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'default_background.png')
        if os.path.exists(self.default_background_path):
            self.set_background_image(self.default_background_path)

        self.object_type_letter_used = {}  # Track used letter indices for each type
        self.possible_child_names = ["foo", "bar", "fb"]  # Static list of possible children
        self.child_graphics = {}  # Map parent obj to list of (child_marker, line)

        # Add preview state tracking
        self.preview_mode = False
        self.preview_time_point = None
        self.stored_positions = {}  # Store positions during preview

        self.portee_enabled = True  # Global portée flag
        self.portee1_enabled = True
        self.portee2_enabled = True
        self.portee3_enabled = True

        # --- Add to MainWindow.__init__ ---
        self.object_creation_locked = False
        self.object_deletion_locked = False
        self.sequence_state = 'Idle'  # Possible: Idle, Playing, Paused, Finished
        print('[DEBUG] Initial state: Idle, object creation/deletion unlocked')

        self.update_health_bar_panel_visibility()

        self.damage_log = []  # Track all damage events for fight report

    def setup_ui(self):
        """Sets up the user interface elements."""
        # Main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_widget.setLayout(main_layout)

        # ------------------------------------
        # Right-side: SimulationView & Controls
        # ------------------------------------
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        main_layout.addLayout(right_layout, 4)

        # Create SimulationView first
        self.view = SimulationView(parent=self)
        self.view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        right_layout.addWidget(self.view, stretch=1)

        # -----------------------------------
        # Left-side palette (QDockWidget)
        # -----------------------------------
        self.palette_dock = QDockWidget("Palette", self)
        self.palette_dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.palette_dock.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable)

        self.palette = ComprehensivePalette(main_window=self, parent=self)
        self.palette_dock.setWidget(self.palette)
        self.addDockWidget(Qt.LeftDockWidgetArea, self.palette_dock)

        # Add Live Objects dock
        self.live_objects_dock = QDockWidget("Live Objects", self)
        self.live_objects_dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.live_objects_list = QTreeWidget()
        self.live_objects_list.setHeaderHidden(True)
        self.live_objects_list.setColumnCount(1)
        
        # Connect the itemClicked signal properly
        self.live_objects_list.itemClicked.connect(self._handle_item_clicked)
        
        self.live_objects_dock.setWidget(self.live_objects_list)
        self.addDockWidget(Qt.RightDockWidgetArea, self.live_objects_dock)

        # Create drawing tools palette
        drawing_tools_dock = self.create_drawing_tools_palette()
        self.addDockWidget(Qt.RightDockWidgetArea, drawing_tools_dock)

        # Create Animation Panel (PowerPoint-style)
        self.animation_panel_dock = QDockWidget("🎬 Animation Panel", self)
        self.animation_panel_dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.animation_panel = AnimationPanel(main_window=self, parent=self)
        self.animation_panel_dock.setWidget(self.animation_panel)
        self.addDockWidget(Qt.RightDockWidgetArea, self.animation_panel_dock)

        # Connect signals after creating all UI elements
        self.view.scene_clicked.connect(self.handle_scene_click_for_geotiff)
        self.view.drawing_finished.connect(self.handle_drawing_finished)
        # Fresh trajectory connection
        self.view.trajectory_completed.connect(self.handle_trajectory_completed)
        self.view.scene_clicked.connect(self.update_coordinates)
        self.view.point_selected.connect(self.handle_point_selected)

    def handle_trajectory_completed(self, unit_name, trajectory_points):
        """Fresh trajectory completion handler"""
        if unit_name and trajectory_points and len(trajectory_points) >= 2:
            # Store in clean trajectory system
            self.unit_trajectories[unit_name] = trajectory_points

            print(f"✅ FRESH TRAJECTORY SAVED: {unit_name} with {len(trajectory_points)} points")
            print(f"   📍 Start: ({trajectory_points[0].x():.1f}, {trajectory_points[0].y():.1f})")
            print(f"   🎯 End: ({trajectory_points[-1].x():.1f}, {trajectory_points[-1].y():.1f})")

            # Refresh the Animation Panel
            if hasattr(self, 'animation_panel'):
                self.animation_panel.refresh_animation_list()

            # Trajectory saved successfully - no need to update button states for this feature
        else:
            print(f"❌ TRAJECTORY ERROR: Invalid data - {unit_name}, {len(trajectory_points) if trajectory_points else 0} points")

    def _handle_item_clicked(self, item, column=0):
        """Internal handler for item clicks that ensures proper argument passing"""
        print(f"[DEBUG] _handle_item_clicked - Item: {item}, Column: {column}")
        try:
            self.on_item_clicked(item)
        except Exception as e:
            print(f"[ERROR] Error in _handle_item_clicked: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_item_clicked(self, item):
        """Handle tree item clicks"""
        print(f"[DEBUG] on_item_clicked - Item clicked: {item}")
        print(f"[DEBUG] Item text: {item.text(0) if item else 'None'}")
        
        try:
            # Check if item is valid
            if not item:
                print("[DEBUG] No item provided")
                return
                
            # Find the top-level item
            top_item = item
            while top_item.parent():
                top_item = top_item.parent()
            
            print(f"[DEBUG] Using top-level item: {top_item.text(0)}")
            
            # Get the object data
            obj = top_item.data(0, Qt.UserRole)
            print(f"[DEBUG] Object from item data: {obj}")
            
            if obj:
                print(f"[DEBUG] Object name: {obj.name if hasattr(obj, 'name') else 'No name'}")
                print(f"[DEBUG] Object type: {obj.object_type if hasattr(obj, 'object_type') else 'No type'}")
                print(f"[DEBUG] Has graphic: {hasattr(obj, 'graphic')}")
                
                # Call sync with the top-level item
                self.sync_palette_selection_to_map(top_item)
            else:
                print("[DEBUG] No object data found")
                
        except Exception as e:
            print(f"[ERROR] Error in on_item_clicked: {str(e)}")
            import traceback
            traceback.print_exc()

    def sync_palette_selection_to_map(self, item):
        """Synchronize palette selection with map display"""
        print(f"[DEBUG] sync_palette_selection_to_map - Starting with item: {item}")
        
        if not item:
            print("[DEBUG] No item provided")
            return
            
        try:
            print(f"[DEBUG] Item text: {item.text(0)}")
            
            # Clear previous selection
            print("[DEBUG] Clearing previous selection")
            self.view.scene.clearSelection()
            self.remove_all_info_labels()
            
            # Collapse all items first
            print("[DEBUG] Collapsing all items")
            root = self.live_objects_list.invisibleRootItem()
            for i in range(root.childCount()):
                root.child(i).setExpanded(False)
            
            # Get the top-level item if this is a child item
            top_item = item
            while top_item.parent():
                top_item = top_item.parent()
                
            print(f"[DEBUG] Using top-level item: {top_item.text(0)}")
            top_item.setExpanded(True)
            
            # Get the object data from the top-level item
            obj = top_item.data(0, Qt.UserRole)
            print(f"[DEBUG] Object from top-level item: {obj}")
            
            if obj:
                print(f"[DEBUG] Object name: {obj.name if hasattr(obj, 'name') else 'No name'}")
                print(f"[DEBUG] Has graphic: {hasattr(obj, 'graphic')}")
                
                if hasattr(obj, 'graphic'):
                    print("[DEBUG] Setting graphic selection")
                    obj.graphic.setSelected(True)
                    print("[DEBUG] Showing info labels")
                    self.show_info_labels(obj)
                else:
                    print("[WARNING] Object has no graphic attribute")
            else:
                print("[WARNING] No object data found in item")
                
        except Exception as e:
            print(f"[ERROR] Error in sync_palette_selection_to_map: {str(e)}")
            import traceback
            traceback.print_exc()

    def handle_point_selected(self, pos):
        """Handle a point selection from the view."""
        if self.active_background_type == 'geotiff' and self.geotiff_ds:
            xyz = self.get_xyz_from_scene_point(pos)
            if xyz:
                lon, lat, z = xyz
                print("____________________________________")
                print(lon)
                print(lat)
                print(z)
                print("____________________________________")
                self.add_geotiff_point(lon, lat, z)
                print(f"Selected point: Lon={lon:.6f}, Lat={lat:.6f}, Z={z:.2f}")
            

    def update_coordinates(self, pos):
        """Update coordinate display when mouse moves."""
        if self.active_background_type == 'geotiff' and self.geotiff_ds:
            # Don't update coordinates if we're dragging objects
            if self.view.scene.mouseGrabberItem():
                return
                
            xyz = self.get_xyz_from_scene_point(pos)
            if xyz:
                lon, lat, z = xyz
                z_str = f"{z:.2f}" if not np.isnan(z) else "NaN"
                # s elf.coords_label.setText(f"Position: Lon={lon:.6f}, Lat={lat:.6f}, Z={z_str}")
                self.coords_label.setText(f"Position:\nLon = {lon:.6f}\nLat = {lat:.6f}\nZ = {z_str}")

                # If drawing, calculate distance
                if hasattr(self.view, 'start_point') and self.view.start_point:
                    start_xyz = self.get_xyz_from_scene_point(self.view.start_point)
                    if start_xyz:
                        distance = self.calculate_distance_3d(start_xyz, xyz)
                        self.distance_label.setText(f"Distance: {distance:.2f} meters")
            else:
                self.coords_label.setText("Position: Not available")
                self.distance_label.setText("Distance: Not available")
        else:
            self.coords_label.setText("Position: Not available")
            self.distance_label.setText("Distance: Not available")

    # ------------------------------------------------------------
    # Fullscreen Logic
    # ------------------------------------------------------------
    def toggle_geotiff_fullscreen(self):
        """Toggles fullscreen mode for the entire application window."""
        if not hasattr(self, 'is_fullscreen'):
            self.is_fullscreen = False
        if not self.is_fullscreen:
            self.showFullScreen()
            self.is_fullscreen = True
        else:
            self.showNormal()
            self.is_fullscreen = False

    def keyPressEvent(self, event):
        """Handle key presses, specifically Esc for exiting fullscreen and Delete for removing selected objects."""
        if event.key() == Qt.Key_Escape and self.is_view_fullscreen:
            self.toggle_geotiff_fullscreen() # Exit fullscreen
        elif event.key() == Qt.Key_Delete:
            selected_items = self.view.scene.selectedItems()
            to_remove = []
            for item in selected_items:
                obj = self.find_object_by_graphic(item)
                if obj:
                    to_remove.append(obj)
            for obj in to_remove:
                if self.object_deletion_locked:
                    print('[DEBUG] Object deletion blocked: scenes exist.')
                    QMessageBox.information(self, 'Action Blocked', 'You cannot delete objects after starting scene creation. Please add and arrange all objects before saving your first scene.')
                    return
                obj.remove()
                self.remove_live_object_from_palette(obj)
                if obj in self.objects:
                    self.objects.remove(obj)
            return
        else:
            # Handle other key presses or pass to base class
            super().keyPressEvent(event)


    # ------------------------------------------------------------
    # Background Logic
    # ------------------------------------------------------------
    def set_background_image(self, image_path, is_geotiff=False):
        """Sets the background image, handling different types."""
        # If setting a non-GeoTIFF background, clear any existing GeoTIFF items
        if not is_geotiff:
            if self.geotiff_tiff_pixmap_item:
                self.view.scene.removeItem(self.geotiff_tiff_pixmap_item)
                self.geotiff_tiff_pixmap_item = None
            if self.geotiff_png_pixmap_item:
                self.view.scene.removeItem(self.geotiff_png_pixmap_item)
                self.geotiff_png_pixmap_item = None
            # Also clear points/markers if switching away from GeoTIFF
            if self.active_background_type == 'geotiff':
                 self.clear_geotiff_points()
            self.active_background_type = 'captured' # Assume non-geotiff is captured or default

        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            pixmap = pixmap.scaled(
                self.view.width(),
                self.view.height(),
                Qt.KeepAspectRatioByExpanding,
                Qt.SmoothTransformation
            )
            # Remove old default/captured background item if it exists
            if self.background_item:
                self.view.scene.removeItem(self.background_item)
                self.background_item = None

            # Add new default/captured background pixmap
            self.background_item = QGraphicsPixmapItem(pixmap)
            self.background_item.setZValue(-1) # Place behind simulation objects
            self.view.scene.addItem(self.background_item)
        else:
            print("Invalid image for background.")

    def capture_and_set_background(self):
        # Clear GeoTIFF background if switching to captured background
        if self.active_background_type == 'geotiff':
            if self.geotiff_tiff_pixmap_item:
                self.view.scene.removeItem(self.geotiff_tiff_pixmap_item)
                self.geotiff_tiff_pixmap_item = None
            if self.geotiff_png_pixmap_item:
                self.view.scene.removeItem(self.geotiff_png_pixmap_item)
                self.geotiff_png_pixmap_item = None
            self.clear_geotiff_points()

        self.hide()
        QApplication.processEvents()
        QTimer.singleShot(300, self.capture_screenshot)

    def capture_screenshot(self):
        screen = QApplication.primaryScreen()
        screenshot = screen.grabWindow(0)
        screenshot_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', "captured_background.png")
        screenshot.save(screenshot_path, "PNG")
        self.show()
        self.set_background_image(screenshot_path, is_geotiff=False) # Explicitly set as non-geotiff
        self.active_background_type = 'captured'

    def resizeEvent(self, event):
        """Handle view resize."""
        super().resizeEvent(event)
        if hasattr(self, 'view') and hasattr(self, 'geotiff_tiff_pixmap_item') and self.geotiff_tiff_pixmap_item is not None:
            # Use the new fit_to_rect method
            self.view.setSceneRect(self.geotiff_tiff_pixmap_item.boundingRect())
            self.view.fitInView(self.geotiff_tiff_pixmap_item.boundingRect(), Qt.KeepAspectRatioByExpanding)
            self.view.updateSceneRect()

    # ------------------------------------------------------------
    # Object Creation Methods
    # ------------------------------------------------------------
    def add_object_tree(self, item, column):
        """
        Invoked when a user double-clicks an item in the QTreeWidget.
        'item' is the QTreeWidgetItem; 'column' is the column index.
        """
        object_type = item.data(0, Qt.UserRole)
        if object_type:
            print(f"[Double-Click] Creating object: {object_type}")
            # Calculate the center of the view in scene coordinates
            center_scene_pos = self.view.mapToScene(self.view.viewport().rect().center())
            # Create the object at the center of the view
            self.create_object_by_type(object_type, (center_scene_pos.x(), center_scene_pos.y()))

    def create_object_by_type(self, object_type, position=(700, 100)):
        print('[DEBUG] Attempting to add object...')
        if self.object_creation_locked:
            print('[DEBUG] Object creation blocked: scenes exist.')
            QMessageBox.information(self, 'Action Blocked', 'You cannot add new objects after starting scene creation. Please add all objects before saving your first scene.')
            return
        # Check if position is within map boundaries
        if not self.is_position_within_map(position):
            QMessageBox.warning(self, "Warning", "Cannot place object outside map boundaries.")
            return
        # Check for scenario objects
        scenario_map = {
            "gpt_moto": gpt_moto,
            "gpt_meca": gpt_meca,
            "S_gpt_moto": S_gpt_moto,
            "S_gpt_meca": S_gpt_meca,
            "enmi_brigade": enmi_brigade,
            "enmi_section": enmi_section,
            "enmi_company": enmi_company,
            "Brigade_meca": Brigade_meca,
            "Brigade_moto": Brigade_moto,
            "Groupement_meca": Groupement_meca,
            "Groupement_moto": Groupement_moto,
            "Bataillon_meca": Bataillon_meca,
            "Bataillon_moto": Bataillon_moto,
            "Compagnie_meca": Compagnie_meca,
            "Compagnie_moto": Compagnie_moto,
            "Section_meca": Section_meca,
            "Section_moto": Section_moto,
            "Sous_groupement_meca": Sous_groupement_meca,
            "Sous_groupement_moto": Sous_groupement_moto,
            "Bataillon_meca_Ennemi": Bataillon_meca_Ennemi,
            "Bataillon_moto_Ennemi": Bataillon_moto_Ennemi,
            "Brigade_meca_Ennemi": Brigade_meca_Ennemi,
            "Brigade_moto_Ennemi": Brigade_moto_Ennemi,
            "Compagnie_meca_Ennemi": Compagnie_meca_Ennemi,
            "Compagnie_moto_Ennemi": Compagnie_moto_Ennemi,
            "Groupement_meca_Ennemi": Groupement_meca_Ennemi,
            "Groupement_moto_Ennemi": Groupement_moto_Ennemi,
            "Section_meca_Ennemi": Section_meca_Ennemi,
            "Section_moto_Ennemi": Section_moto_Ennemi,
            "Sous_groupement_meca_Ennemi": Sous_groupement_meca_Ennemi,
            "Sous_groupement_moto_Ennemi": Sous_groupement_moto_Ennemi,
            "Cas": Cas,
            "Cml": Cml,
            "Esc": Esc,
            "Geb": Geb,
            "Reco": Reco,
            "Sac": Sac,
            "Jeep_ami": Jeep_ami,
            "Tank_ami": Tank_ami,
            "Jeep_ennemi": Jeep_ennemi,
            "Tank_ennemi": Tank_ennemi,
            "M113_ami": M113_ami,
            "M113_ennemi": M113_ennemi
            

        }
        if object_type in scenario_map:
            scenario_class = scenario_map[object_type]()
            # Assign unique letter (reuse freed letters)
            letter, letter_idx = self.get_next_letter_for_type(scenario_class.name)
            display_name = f"{scenario_class.name}_{letter}"
            obj = SimulationObject(
                name=display_name,
                object_type=scenario_class.name,
                position=position,
                space=self.physics_space,
                scene=self.view.scene,
                icon_size=getattr(scenario_class, 'icon_size', 40),
                color=QColor(scenario_class.color)
            )
            obj._letter_idx = letter_idx  # Store for reuse on delete
            # Set color from scenario class
            obj.color = QColor(scenario_class.color)
            # Copy unit composition and firepower from scenario_class to obj
            for field in ['personnel_count', 'vehicle_count', 'mechanized_count', 'tank_count', 'ap_count', 'ac_count', 'missile_count']:
                if hasattr(scenario_class, field):
                    setattr(obj, field, getattr(scenario_class, field))
            # Copy speed from scenario_class if available
            if hasattr(scenario_class, 'speed'):
                obj.base_speed = scenario_class.speed
                obj.current_speed = scenario_class.speed
            else:
                obj.base_speed = 5
                obj.current_speed = 5
            # Set max_health after copying unit counts
            obj.max_health = obj.total_health()
            obj.update_health_bar()
            if isinstance(obj.graphic, (QGraphicsEllipseItem, QGraphicsRectItem)):
                obj.graphic.setBrush(QBrush(obj.color))
            # Set health bar visibility to match the toggle
            if hasattr(self.palette, 'health_bar_toggle'):
                obj.set_health_bar_visible(self.palette.health_bar_toggle.isChecked())
            self.objects.append(obj)
            self.add_live_object_to_palette(obj)
            self.update_health_bar_panel_visibility()
            # --- Sync portée state with UI panel ---
            if self.portee_enabled:
                obj.set_portee_visible(1, self.portee1_enabled)
                obj.set_portee_visible(2, self.portee2_enabled)
                obj.set_portee_visible(3, self.portee3_enabled)
            else:
                obj.set_portee_visible(1, False)
                obj.set_portee_visible(2, False)
                obj.set_portee_visible(3, False)
            # When creating the object, set its initial time-specific image
            if hasattr(self, 'scenes') and self.scenes:
                current_time = self.scenes[self.current_scene_index]['time']
                self.update_object_image_for_time(obj, current_time)
            print('[DEBUG] Object created successfully.')
            return
        if object_type in ["Car", "Truck", "Bus"]:
            self.create_vehicle(object_type, position)
        elif object_type in ["House", "Tree"]:
            self.create_environment(object_type, position)
        elif object_type in ["Person", "Robot"]:
            self.create_character(object_type, position)
        elif object_type in ["Ball", "Box"]:
            if object_type == "Ball":
                self.create_ball(position)
            else:
                self.create_box(position)
        else:
            print(f"No specialized creator for '{object_type}'. Creating a Box instead.")
            self.create_box(position)

    def is_position_within_map(self, position):
        """Checks if a position is within the map boundaries."""
        if not self.geotiff_tiff_pixmap_item:
            return False

        # Get map boundaries
        map_rect = self.geotiff_tiff_pixmap_item.boundingRect()
        map_pos = self.geotiff_tiff_pixmap_item.pos()
        
        # Check if position is within map boundaries
        return (map_pos.x() <= position[0] <= map_pos.x() + map_rect.width() and
                map_pos.y() <= position[1] <= map_pos.y() + map_rect.height())

    def create_vehicle(self, subtype, position):
        print(f"[create_vehicle] {subtype} at {position}")
        obj = SimulationObject(
            name=f"{subtype}{len(self.objects)+1}",
            object_type=subtype,
            position=position,
            space=self.physics_space,
            scene=self.view.scene
        )
        self.objects.append(obj)

    def create_environment(self, subtype, position):
        print(f"[create_environment] {subtype} at {position}")
        obj = SimulationObject(
            name=f"{subtype}{len(self.objects)+1}",
            object_type=subtype,
            position=position,
            space=self.physics_space,
            scene=self.view.scene
        )
        self.objects.append(obj)

    def create_character(self, subtype, position):
        print(f"[create_character] {subtype} at {position}")
        obj = SimulationObject(
            name=f"{subtype}{len(self.objects)+1}",
            object_type=subtype,
            position=position,
            space=self.physics_space,
            scene=self.view.scene
        )
        self.objects.append(obj)

    def create_ball(self, position=(700, 100)):
        obj = SimulationObject(
            name=f"Ball{len(self.objects)+1}",
            object_type="Ball",
            position=position,
            space=self.physics_space,
            scene=self.view.scene
        )
        self.objects.append(obj)

    def create_box(self, position=(700, 100)):
        obj = SimulationObject(
            name=f"Box{len(self.objects)+1}",
            object_type="Box",
            position=position,
            space=self.physics_space,
            scene=self.view.scene
        )
        self.objects.append(obj)

    # ------------------------------------------------------------
    # Simulation Methods 
    # ------------------------------------------------------------
    def update_simulation(self):
        import time
        now = time.time()
        FIRE_INTERVALS = {'AP': 0.5, 'AC': 1.0, 'Missile': 2.0}  # seconds
        # Debug: show which list is being used
        if hasattr(self, 'live_objects'):
            print(f"[DEBUG] update_simulation: live_objects count = {len(self.live_objects)} names = {[obj.name for obj in self.live_objects]}")
        else:
            print(f"[DEBUG] update_simulation: live_objects attribute missing!")
        print(f"[DEBUG] update_simulation: objects count = {len(self.objects)} names = {[obj.name for obj in self.objects]}")
        # Only run damage logic in simulation mode
        print('===============================================')
        print(f"[DEBUG] update_simulation: simulation_running = {getattr(self, 'simulation_running', False)}")
        if getattr(self, 'simulation_running', False):
            for obj in self.objects:
                print(f"[DEBUG] update_simulation: processing object {obj.name}")
                for portee_type in ['AP', 'AC', 'Missile']:
                    overlaps = obj.sync_simulation_update_and_overlap(portee_type)
                    targets = [other for (other, ptype, overlap_type) in overlaps]
                    print(f"[DEBUG] {obj.name} {portee_type} targets: {[t.name for t in targets]}")
                    if targets:
                        last_fire = getattr(obj, 'last_fire_time', {}).get(portee_type, 0)
                        interval = FIRE_INTERVALS[portee_type]
                        print(f"[DEBUG] {obj.name} {portee_type} last_fire={last_fire:.2f} now={now:.2f} interval={interval}")
                        if now - last_fire >= interval:
                            print(f"[FIRE] {obj.name} fires {portee_type} at {[t.name for t in targets]}")
                            obj.attack_targets(targets, portee_type)
                            obj.last_fire_time[portee_type] = now
            self.update_live_object_health_bars()
        # Always update graphics/UI
        for obj in self.objects:
            obj.update_graphics()

    def update_live_object_health_bars(self):
        # Update QProgressBar in live objects panel to match SimulationObject state
        if hasattr(self, '_health_bar_widgets'):
            for obj, bar in self._health_bar_widgets.items():
                init_health = getattr(obj, 'init_health', None)
                if init_health is None or init_health == 0:
                    # fallback to current health if not set
                    init_health = obj.total_health() if hasattr(obj, 'total_health') else 1
                health = obj.total_health() if hasattr(obj, 'total_health') else init_health
                bar.setMaximum(int(init_health))
                bar.setValue(int(health))
                bar.setFormat(f"Health: {health:.1f}/{init_health:.1f}")
        # Also update health bar for any new objects
        if hasattr(self, 'live_objects'):
            for obj in self.live_objects:
                if hasattr(obj, 'update_health_bar'):
                    obj.update_health_bar()

    def play_simulation(self):
        print("[DEBUG][SIM] play_simulation called (no timer, manual updates only)")
        # No timer logic

    def pause_simulation(self):
        print("[DEBUG][SIM] pause_simulation called (no timer, manual updates only)")
        # No timer logic

    def reset_simulation(self):
        """Resets the simulation to initial state"""
        self.current_scene_index = 0
        if self.scenes:
            self.animate_to_scene(0)  # This will also update the images to T1
        # Reset all units' health and counts
        for obj in self.objects:
            if hasattr(obj, 'reset_to_initial_state'):
                obj.reset_to_initial_state()
        self.update_live_object_health_bars()  # Force UI update of all health bars
        self.animation_in_progress = False
        # --- Disable Simulation Report button on reset ---
        if hasattr(self, 'palette') and hasattr(self.palette, 'sim_report_btn'):
            self.palette.sim_report_btn.setEnabled(False)
        print("Simulation reset to initial state")

    def switch_mode(self):
        # Ensure mode_button exists and is in the UI
        if not hasattr(self, 'mode_button'):
            self.mode_button = QPushButton("Switch to Simulation Mode")
            self.mode_button.clicked.connect(self.switch_mode)
            # Add to main layout if not present
            if hasattr(self, 'centralWidget') and self.centralWidget():
                layout = self.centralWidget().layout()
                if layout and self.mode_button not in [layout.itemAt(i).widget() for i in range(layout.count()) if layout.itemAt(i).widget()]:
                    layout.addWidget(self.mode_button)
        if self.mode_button.text() == "Switch to Simulation Mode":
            self.simulation_running = True
            self.play_scene_sequence()  # Start scene animation
            self.mode_button.setText("Switch to Editing Mode")
            self.palette.setEnabled(False)
            print("[SIM] Switched to Simulation Mode and started scene playback.")
        else:
            self.simulation_running = False
            self.pause_scene_sequence()  # Pause scene animation
            self.mode_button.setText("Switch to Simulation Mode")
            self.palette.setEnabled(True)
            print("[SIM] Switched to Editing Mode and paused scene playback.")

    # ------------------------------------------------------------
    # Color Picker Method
    # ------------------------------------------------------------
    def change_color(self):
        selected_items = self.view.scene.selectedItems()
        if selected_items:
            color = QColorDialog.getColor()
            if color.isValid():
                item = selected_items[0]
                # Update the color in the SimulationObject
                obj = self.find_object_by_graphic(item)
                if obj:
                    obj.color = color
                    if isinstance(item, (QGraphicsEllipseItem, QGraphicsRectItem)):
                        item.setBrush(QBrush(color))
                    elif isinstance(item, QGraphicsPixmapItem):
                        # For pixmap items, apply a color overlay
                        self.apply_color_overlay(item, color)

    def find_object_by_graphic(self, graphic_item):
        for obj in self.objects:
            if obj.graphic == graphic_item:
                return obj
        return None

    def apply_color_overlay(self, pixmap_item, color):
        """
        Applies a semi-transparent color overlay to a QGraphicsPixmapItem.
        """
        # Create a colored rectangle with transparency
        overlay = QGraphicsRectItem(pixmap_item.boundingRect(), pixmap_item)
        overlay.setBrush(QBrush(color))
        overlay.setOpacity(0.5)
        overlay.setZValue(2)
        self.view.scene.addItem(overlay)

    # --- GeoTIFF Methods (Copied and adapted from geotif_elevation_V8.py) ---

    def check_gdal(self):
        """Checks if GDAL is available and shows a message if not."""
        if not GDAL_AVAILABLE:
            QMessageBox.critical(self, "Missing Dependency",
                                 "GDAL Python bindings are required for GeoTIFF features but were not found.\n"
                                 "Please install the 'gdal' Python package (e.g., using pip or conda) "
                                 "and ensure GDAL libraries are correctly configured.")
            return False
        # Ensure GDAL exceptions are enabled if available
        # Do this once at startup? Or here? Let's do it here for safety.
        gdal.UseExceptions()
        return True

    def load_geotiff(self):
        """Load and display a GeoTIFF file."""
        if not self.check_gdal():
            return

        try:
            file_name, _ = QFileDialog.getOpenFileName(
                self, "Open GeoTIFF", "", "GeoTIFF Files (*.tif *.tiff);;All Files (*)"
            )

            if not file_name:
                print("No file selected")
                return

            # Load the GeoTIFF dataset
            self.geotiff_ds = gdal.Open(file_name)
            if self.geotiff_ds is None:
                QMessageBox.critical(self, "Error", "Could not load GeoTIFF file.")
                print(f"Failed to open GeoTIFF file: {file_name}")
                return

            print(f"Loaded GeoTIFF: {os.path.basename(file_name)}")
            
            # Store the geotransform for coordinate conversion
            self.geotiff_geotransform = self.geotiff_ds.GetGeoTransform()
            if not self.calculate_inverse_geotransform():
                 return

            # Get elevation data
            band = self.geotiff_ds.GetRasterBand(1)
            self.geotiff_image_array = band.ReadAsArray()
            
            # Create the display
            self.create_tiff_display()
            
            # Initialize the points list if not already done
            if not hasattr(self, 'geotiff_points'):
                self.geotiff_points = []

            # Clear any existing points when loading a new file
            self.clear_geotiff_points()
            
            # Set active background type
            self.active_background_type = 'geotiff'

        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred while loading the GeoTIFF:\n{str(e)}")
            print(f"Error loading GeoTIFF: {str(e)}")
            # Cleanup
            if hasattr(self, 'geotiff_ds'):
             self.geotiff_ds = None
            if hasattr(self, 'geotiff_tiff_pixmap_item') and self.geotiff_tiff_pixmap_item is not None:
                self.view.scene.removeItem(self.geotiff_tiff_pixmap_item)
                self.geotiff_tiff_pixmap_item = None

    def create_tiff_display(self):
        """Create the display for the GeoTIFF."""
        if not hasattr(self, 'geotiff_ds') or self.geotiff_ds is None:
            print("Error: No GeoTIFF dataset available")
            return

        try:
            # Get the first band (assuming it's grayscale or using first band of RGB)
            band = self.geotiff_ds.GetRasterBand(1)
            
            # Read the entire band into a numpy array
            self.geotiff_data = band.ReadAsArray()
            
            # Get image dimensions
            width = self.geotiff_ds.RasterXSize
            height = self.geotiff_ds.RasterYSize
            
            # Create QImage from numpy array
            if self.geotiff_ds.RasterCount == 1:
                # For single band, create grayscale image
                bytes_per_line = width
                q_image = QImage(self.geotiff_data.data, width, height, bytes_per_line, QImage.Format_Grayscale8)
            else:
                # For RGB, assuming standard byte order
                bytes_per_line = 3 * width
                q_image = QImage(self.geotiff_data.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # Convert to QPixmap
            pixmap = QPixmap.fromImage(q_image)
            
            # Remove existing pixmap item if it exists
            if hasattr(self, 'geotiff_tiff_pixmap_item') and self.geotiff_tiff_pixmap_item is not None:
                self.view.scene.removeItem(self.geotiff_tiff_pixmap_item)
                self.geotiff_tiff_pixmap_item = None

            # Create new pixmap item
            self.geotiff_tiff_pixmap_item = QGraphicsPixmapItem(pixmap)
            self.view.scene.addItem(self.geotiff_tiff_pixmap_item)
            self.view.add_to_layer(self.geotiff_tiff_pixmap_item, "geotiff")
            
            # Store the geotransform for coordinate conversion
            self.geotiff_transform = self.geotiff_ds.GetGeoTransform()
            self.calculate_inverse_geotransform()

            # Set the active background type
            self.active_background_type = 'geotiff'

            # Ensure the GeoTIFF is visible and properly scaled
            self.view.setSceneRect(self.geotiff_tiff_pixmap_item.boundingRect())
            self.view.fitInView(self.geotiff_tiff_pixmap_item.boundingRect(), Qt.KeepAspectRatioByExpanding)
            self.view.updateSceneRect()
            
            print("GeoTIFF display created successfully")
            
            # Notify palette that GeoTIFF is loaded
            self.palette.on_geotiff_loaded()
            
        except Exception as e:
            print(f"Error creating GeoTIFF display: {str(e)}")
            if hasattr(self, 'geotiff_tiff_pixmap_item') and self.geotiff_tiff_pixmap_item is not None:
                  self.view.scene.removeItem(self.geotiff_tiff_pixmap_item)
                  self.geotiff_tiff_pixmap_item = None
 
    def load_png_overlay(self):
        """Load a PNG file as an overlay on the GeoTIFF."""
        if not hasattr(self, 'geotiff_tiff_pixmap_item') or not self.geotiff_tiff_pixmap_item:
            QMessageBox.warning(self, "Warning", "Please load a GeoTIFF file first.")
            return

        file_name, _ = QFileDialog.getOpenFileName(
            self, "Open PNG Overlay", "", "PNG Files (*.png);;All Files (*)"
        )

        if file_name:
            try:
                image = QImage(file_name)
                if image.isNull():
                    QMessageBox.critical(self, "Error", "Could not load PNG file.")
                    return

                # Create the PNG overlay
                self.create_png_display(image)
                
                # Notify palette that PNG is loaded
                if hasattr(self, 'palette'):
                    self.palette.on_png_loaded()
                    
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load PNG overlay: {str(e)}")
                print(f"Error loading PNG overlay: {str(e)}")

    def create_png_display(self, png_image):
        """Creates and positions the PNG overlay with proper georeferencing."""
        if not hasattr(self, 'geotiff_tiff_pixmap_item') or not self.geotiff_tiff_pixmap_item:
            print("Error: Cannot add PNG overlay without GeoTIFF base")
            return

        try:
            # Remove existing PNG overlay if it exists
            if hasattr(self, 'geotiff_png_pixmap_item') and self.geotiff_png_pixmap_item:
             self.view.scene.removeItem(self.geotiff_png_pixmap_item)
             self.geotiff_png_pixmap_item = None

            # Create a new QImage with transparency support
            transparent_image = QImage(png_image.size(), QImage.Format_ARGB32_Premultiplied)
            transparent_image.fill(Qt.transparent)
            
            # Create painter to draw the original image
            painter = QPainter(transparent_image)
            painter.setCompositionMode(QPainter.CompositionMode_Source)
            painter.drawImage(0, 0, png_image)
            painter.end()
            
            # Convert to pixmap
            pixmap = QPixmap.fromImage(transparent_image)
            
            # Create the pixmap item
            self.geotiff_png_pixmap_item = QGraphicsPixmapItem(pixmap)
            
            # Get GeoTIFF dimensions and transform
            tiff_rect = self.geotiff_tiff_pixmap_item.boundingRect()
            tiff_transform = self.geotiff_transform
            
            # Calculate scaling to match GeoTIFF coordinates
            scale_x = tiff_rect.width() / png_image.width()
            scale_y = tiff_rect.height() / png_image.height()
            scale = min(scale_x, scale_y)
            
            # Apply transform to PNG
            self.geotiff_png_pixmap_item.setScale(scale)
            
            # Position PNG to align with GeoTIFF coordinates
            tiff_pos = self.geotiff_tiff_pixmap_item.pos()
            png_width = png_image.width() * scale
            png_height = png_image.height() * scale
            x_offset = (tiff_rect.width() - png_width) / 2
            y_offset = (tiff_rect.height() - png_height) / 2
            
            # Store the PNG's georeference information
            self.png_transform = {
                'scale': scale,
                'offset_x': x_offset,
                'offset_y': y_offset,
                'tiff_transform': tiff_transform
            }
            
            # Position the PNG
            self.geotiff_png_pixmap_item.setPos(tiff_pos.x() + x_offset, tiff_pos.y() + y_offset)
            
            # Add to scene in content layer
            self.view.scene.addItem(self.geotiff_png_pixmap_item)
            self.view.add_to_layer(self.geotiff_png_pixmap_item, "png")
            
            print("PNG overlay added successfully with georeferencing")
            
        except Exception as e:
            print(f"Error creating PNG overlay: {str(e)}")
            if hasattr(self, 'geotiff_png_pixmap_item') and self.geotiff_png_pixmap_item:
                self.view.scene.removeItem(self.geotiff_png_pixmap_item)
                self.geotiff_png_pixmap_item = None
            raise  # Re-raise the exception to be caught by the caller

    def calculate_inverse_geotransform(self):
        """Calculates the inverse of the GeoTIFF's geotransform. Returns True/False."""
        if not self.geotiff_geotransform: return False
        inv_gt = gdal.InvGeoTransform(self.geotiff_geotransform)
        if inv_gt is None:
            QMessageBox.critical(self, "Error", "The GeoTIFF's geotransform matrix is singular.")
            print("Error: Non-invertible geotransform.")
            self.geotiff_inv_geotransform = None
            return False
        else:
            self.geotiff_inv_geotransform = inv_gt
            return True

    # --- GeoTIFF Point/Coordinate Methods ---

    def handle_scene_click_for_geotiff(self, scene_pos):
        """Handles clicks on the scene, potentially for GeoTIFF or exiting fullscreen."""
        # If in fullscreen mode, a click should exit fullscreen
        if self.is_view_fullscreen:
            self.toggle_geotiff_fullscreen()
            return

        # If not in fullscreen and GeoTIFF is not active or ready, do nothing
        if self.active_background_type != 'geotiff' or not self.geotiff_ds:
            return

        # Don't add points if we're dragging objects
        if self.view.scene.mouseGrabberItem():
            return

        # Only add points if we're in point selection mode
        if not hasattr(self.view, 'is_point_selection_mode') or not self.view.is_point_selection_mode:
            return

        xyz = self.get_xyz_from_scene_point(scene_pos)
        if xyz:
            lon, lat, z = xyz
        self.add_geotiff_point(lon, lat, z)
            # print(f"Added GeoTIFF point: Lon={lon:.6f}, Lat={lat:.6f}, Z={z:.2f}")
        # else: do nothing (out of map)

    def scene_to_tiff_pixel(self, scene_pos):
        """
        Converts scene coordinates (QPointF, Y-down) to GeoTIFF pixel coordinates (col, row).
        """
        if not self.geotiff_tiff_pixmap_item:
            print("Error: GeoTIFF item not available for coordinate conversion.")
            return None, None

        # Map scene coordinates to the local coordinate system of the TIFF pixmap item
        item_pos = self.geotiff_tiff_pixmap_item.mapFromScene(scene_pos)

        # Get the item's bounding rect
        rect = self.geotiff_tiff_pixmap_item.boundingRect()
        
        # Calculate pixel coordinates
        col = int(math.floor((item_pos.x() / rect.width()) * self.geotiff_ds.RasterXSize))
        row = int(math.floor((item_pos.y() / rect.height()) * self.geotiff_ds.RasterYSize))

        return col, row


    def tiff_pixel_to_geo(self, col, row):
        """Converts GeoTIFF pixel coordinates (col, row) to geographic coordinates (lon, lat)."""
        if self.geotiff_geotransform is None:
            print("Error: No geotransform available for coordinate conversion.")
            return None, None
            
        gt = self.geotiff_geotransform
        lon = gt[0] + col * gt[1] + row * gt[2]
        lat = gt[3] + col * gt[4] + row * gt[5]
        return lon, lat

    def geo_to_tiff_pixel(self, lon, lat):
        """Converts geographic coordinates (lon, lat) to GeoTIFF pixel coordinates (col, row)."""
        if self.geotiff_inv_geotransform is None:
            print("Error: No inverse geotransform available for coordinate conversion.")
            return None, None
            
        inv_gt = self.geotiff_inv_geotransform
        col_f = inv_gt[0] + lon * inv_gt[1] + lat * inv_gt[2]
        row_f = inv_gt[3] + lon * inv_gt[4] + lat * inv_gt[5]
        return int(math.floor(col_f)), int(math.floor(row_f))

    def add_geotiff_point(self, lon, lat, z):
        """Adds the valid GeoTIFF point and updates displays."""
        self.geotiff_points.append((lon, lat, z))
        self.update_geotiff_display_markers() # This will redraw all markers

        if len(self.geotiff_points) >= 2:
            self.calculate_geotiff_distance()

    def clear_geotiff_points(self):
        """Clears GeoTIFF specific points and markers."""
        self.geotiff_points = []
        # Remove markers specifically
        items_to_remove = [item for item in self.view.scene.items() if isinstance(item, AnimatedEllipseItem)]
        for item in items_to_remove:
            self.view.scene.removeItem(item)
        print("GeoTIFF points cleared.")

    def calculate_geotiff_distance(self):
        """Calculate 3D distance between the last two GeoTIFF points."""
        if len(self.geotiff_points) < 2: return
        p1, p2 = self.geotiff_points[-2], self.geotiff_points[-1]
        lon1, lat1, z1 = p1
        lon2, lat2, z2 = p2
        if np.isnan(z1) or np.isnan(z2):
             return
        h_dist = self.haversine(lon1, lat1, lon2, lat2)
        v_diff = abs(z2 - z1)
        dist_3d = math.sqrt(h_dist**2 + v_diff**2)
        print(f"3D Distance: {dist_3d:.2f} m (H: {h_dist:.2f} m | V: {v_diff:.2f} m)")

    def haversine(self, lon1, lat1, lon2, lat2):
        """Calculate great-circle distance in meters."""
        R = 6371000 # Earth radius
        lon1_rad, lat1_rad, lon2_rad, lat2_rad = map(math.radians, [lon1, lat1, lon2, lat2])
        dlon = lon2_rad - lon1_rad
        dlat = lat2_rad - lat1_rad
        a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        return R * c

    def update_geotiff_display_markers(self):
        """Adds/updates GeoTIFF point markers on the main scene."""
        # Remove existing markers first
        items_to_remove = [item for item in self.view.scene.items() if isinstance(item, AnimatedEllipseItem)]
        for item in items_to_remove:
            self.view.scene.removeItem(item)

        if self.active_background_type != 'geotiff' or not self.geotiff_tiff_pixmap_item:
             return # Cannot place markers if GeoTIFF isn't active or displayed

        # Set fully transparent pen and brush
        pen = QPen(Qt.transparent)
        brush = QBrush(Qt.transparent)
        marker_radius = 5
        marker_diameter = marker_radius * 2

        last_marker_coords = self.geotiff_points[-1][:2] if self.geotiff_points else None

        for lon, lat, _ in self.geotiff_points:
            # --- Convert Geo to Scene ---
            scene_x, scene_y = self.geo_to_scene(lon, lat)
            # --- End Conversion ---

            if scene_x is not None: # Check if conversion succeeded
                marker = AnimatedEllipseItem(
                    scene_x - marker_radius, scene_y - marker_radius,
                    marker_diameter, marker_diameter
                )
                marker.setPen(pen)
                marker.setBrush(brush)
                marker.setZValue(10)
                self.view.scene.addItem(marker)

                # Animate the last marker (now invisible)
                if (lon, lat) == last_marker_coords:
                    anim_proxy = QObject()
                    anim = QPropertyAnimation(anim_proxy, b"dummyValue")
                    anim.setDuration(300)
                    anim.setStartValue(0.1)
                    anim.setEndValue(1.0)
                    anim.setEasingCurve(QEasingCurve.OutBack)
                    anim.valueChanged.connect(lambda value, m=marker: m.setScaleValue(value))
                    anim.start(QPropertyAnimation.DeleteWhenStopped)
                    marker._animation_proxy = anim_proxy

    def geo_to_scene(self, lon, lat):
        """
        Placeholder: Converts geographic coordinates (lon, lat) to scene coordinates (x, y).
        NEEDS IMPLEMENTATION based on SimulationView's coordinate system and the
        position/scale/transform of self.geotiff_tiff_pixmap_item in the scene.
        """
        if self.geotiff_inv_geotransform is None or not self.geotiff_tiff_pixmap_item:
            return None, None # Cannot convert without transform or TIFF item

        # 1. Convert geographic (lon, lat) to TIFF pixel (col, row)
        col, row = self.geo_to_tiff_pixel(lon, lat)
        if col is None: return None, None # Check if geo_to_tiff_pixel failed

        # 2. Convert TIFF pixel (col, row) coordinates to the local coordinates
        #    within the geotiff_tiff_pixmap_item. Since the item's top-left
        #    corresponds to pixel (0,0), the local coordinates are simply (col, row).
        #    We need a QPointF for mapping.
        item_local_pos = QPointF(col, row)

        # 3. Map the item's local coordinates to scene coordinates.
        #    This accounts for the item's position and any transformations applied to it.
        scene_pos = self.geotiff_tiff_pixmap_item.mapToScene(item_local_pos)

        # print(f"Geo: {lon:.6f}, {lat:.6f} -> Pixel: {col}, {row} -> ItemLocal: {item_local_pos.x():.2f}, {item_local_pos.y():.2f} -> Scene: {scene_pos.x():.2f}, {scene_pos.y():.2f}")
        return scene_pos.x(), scene_pos.y()

    # --- End GeoTIFF Methods ---

    # ------------------------------------------------------------
    # Project Setup Dialog
    # ------------------------------------------------------------
    def show_project_setup_dialog(self):
        """Shows the project setup dialog and stores the data."""
        dialog = ProjectSetupDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.project_data = dialog.get_project_data()
            print("Project Data:", self.project_data)
            
            # Notify palette that project setup is complete
            self.palette.on_project_setup_complete(self.project_data)
            
            # Show success message
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Information)
            msg.setText("Project Setup Complete!")
            msg.setInformativeText("You can now start adding objects and using drawing tools.")
            msg.setWindowTitle("Setup Complete")
            msg.exec_()

    # ------------------------------------------------------------
    # Drawing Methods
    # ------------------------------------------------------------
    def set_drawing_mode_in_view(self, mode):
        """Sets the drawing mode in the view and updates UI accordingly."""
        if hasattr(self, 'view'):
            self.view.set_drawing_mode(mode)
            self.statusBar().showMessage(f"Drawing mode: {mode}")
            print(f"Setting drawing mode to: {mode}")  # Debug print

    def _activate_trajectory_mode(self):
        """🎯 NEW CLEAN TRAJECTORY MODE - No pre-selection required!"""
        print("🎯 NEW TRAJECTORY BUTTON CLICKED!")

        # Check if any military units exist
        if not self.objects:
            QMessageBox.information(self, "No Units",
                                  "Please add military units to the map first by dragging them from the palette.")
            return

        # 🎯 NEW SYSTEM: Just activate trajectory mode - selection happens during drawing
        self.view.set_drawing_mode("Draw Trajectory")
        self.statusBar().showMessage("🎯 NEW TRAJECTORY MODE: Select a unit, then click to start drawing path!")
        print("🎯 NEW TRAJECTORY MODE ACTIVATED - Ready to select unit and draw!")

    def undo_last_draw(self):
        """Undoes the last drawing action."""
        self.view.undo_last_draw()
        self.statusBar().showMessage("Undid last drawing")

    def remove_selected_draw(self):
        """Removes the currently selected drawing."""
        self.view.remove_selected_draw()
        self.statusBar().showMessage("Removed selected drawing")

    # ------------------------------------------------------------
    # Handle Finished Drawing and Get XYZ
    # ------------------------------------------------------------
    def handle_drawing_finished(self, item):
        """Processes a finished drawn item to get its XYZ coordinates."""
        if self.active_background_type != 'geotiff' or not self.geotiff_ds or not self.geotiff_inv_geotransform or self.geotiff_image_array is None:
            print("Cannot get XYZ for drawn item: GeoTIFF not active or data missing.")
            return

        drawn_points_xyz = []
        total_distance = 0.0

        if isinstance(item, QGraphicsLineItem):
            line = item.line()
            points = [line.p1(), line.p2()]
            print(f"Processing Line with points: {points}")
            for point in points:
                xyz = self.get_xyz_from_scene_point(point)
                if xyz:
                    drawn_points_xyz.append(xyz)
            if len(drawn_points_xyz) == 2:
                total_distance = self.calculate_distance_3d(drawn_points_xyz[0], drawn_points_xyz[1])

        elif isinstance(item, QGraphicsRectItem):
            rect = item.rect()
            points = [rect.topLeft(), rect.topRight(), rect.bottomRight(), rect.bottomLeft()]
            print(f"Processing Rectangle with points: {points}")
            for point in points:
                xyz = self.get_xyz_from_scene_point(point)
                if xyz:
                    drawn_points_xyz.append(xyz)
            if len(drawn_points_xyz) >= 2:
                # Calculate perimeter
                for i in range(len(drawn_points_xyz)):
                    total_distance += self.calculate_distance_3d(
                        drawn_points_xyz[i],
                        drawn_points_xyz[(i + 1) % len(drawn_points_xyz)]
                    )

        elif isinstance(item, QGraphicsPathItem):
            path = item.path()
            points = []
            for i in range(path.elementCount()):
                element = path.elementAt(i)
                if element.type == QPainterPath.MoveToElement or element.type == QPainterPath.LineToElement:
                    points.append(QPointF(element.x, element.y))

            print(f"Processing Free Line with {len(points)} points.")
            for point in points:
                 xyz = self.get_xyz_from_scene_point(point)
                 if xyz:
                      drawn_points_xyz.append(xyz)

            # Calculate total path length
            for i in range(len(drawn_points_xyz) - 1):
                total_distance += self.calculate_distance_3d(drawn_points_xyz[i], drawn_points_xyz[i + 1])

        # Print or store the collected XYZ points
        if drawn_points_xyz:
            print("\n--- Drawn Item XYZ Coordinates ---")
            for i, (lon, lat, z) in enumerate(drawn_points_xyz):
                 z_str = f"{z:.2f}" if not np.isnan(z) else "NaN"
                 print(f"  Point {i+1}: Lon={lon:.6f}, Lat={lat:.6f}, Z={z_str}")
            print(f"Total Distance: {total_distance:.2f} meters")
            print("----------------------------------\n")
            # Update palette distance label
            self.palette.set_distance(f"Distance: {total_distance:.2f} meters")
        else:
            print("Could not get valid XYZ coordinates for the drawn item.")
            self.palette.set_distance("Distance: Not available")

    def calculate_distance_3d(self, point1, point2):
        """Calculate 3D distance between two points (lon, lat, z)."""
        lon1, lat1, z1 = point1
        lon2, lat2, z2 = point2
        
        # Calculate horizontal distance using haversine
        h_dist = self.haversine(lon1, lat1, lon2, lat2)
        
        # Calculate vertical distance
        v_diff = abs(z2 - z1)
        
        # Calculate 3D distance
        return math.sqrt(h_dist**2 + v_diff**2)

    def get_xyz_from_scene_point(self, scene_pos):
        """Helper to convert a single scene point to XYZ."""
        if not self.geotiff_ds or not self.geotiff_inv_geotransform or self.geotiff_image_array is None:
            print("Error: GeoTIFF data not properly initialized.")
            return None

        # Convert scene to pixel coordinates
        col, row = self.scene_to_tiff_pixel(scene_pos)
        if col is None or row is None:
             return None

        # Check bounds
        if not (0 <= col < self.geotiff_ds.RasterXSize and 0 <= row < self.geotiff_ds.RasterYSize):
            # print(f"Warning: Point outside GeoTIFF bounds: col={col}, row={row}")
            return None

        # Get elevation
        try:
            z = float(self.geotiff_image_array[row, col])
            if np.isnan(z):
                print(f"Warning: No elevation data at pixel ({col}, {row})")
                return None
        except (IndexError, ValueError) as e:
            print(f"Error getting elevation: {e}")
            return None

        # Convert to geographic coordinates
        lon, lat = self.tiff_pixel_to_geo(col, row)
        if lon is None or lat is None:
             return None

        return (lon, lat, z)

    def create_drawing_tools_palette(self):
        """Creates a palette for drawing tools."""
        palette = QDockWidget("Drawing Tools", self)
        palette.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Drawing Tools Group
        drawing_group = QGroupBox("Drawing Tools")
        drawing_grid = QGridLayout()
        drawing_grid.setContentsMargins(4, 4, 4, 4)
        drawing_grid.setHorizontalSpacing(8)
        drawing_grid.setVerticalSpacing(4)
        drawing_grid.setColumnStretch(0, 0)  # Icon column: no stretch
        drawing_grid.setColumnStretch(1, 1)  # Label column: stretches
        btn_style = """
            QPushButton {
                background-color: #e0e0e0;
                border: 1px solid #bbb;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d0d0d0;
                border: 1.5px solid #2196F3;
            }
            QPushButton:pressed {
                background-color: #b0b0b0;
                border: 2px solid #1976D2;
            }
            QPushButton:focus {
                background-color: #f0f8ff;
                border: 2px solid #1976D2;
                outline: none;
            }
        """
        label_style = "font-size: 10px; margin-left: 8px; color: #222;"
        
        # --- Tool: Add Point (+) ---
        point_btn = QPushButton()
        point_btn.setIcon(fugue.icon('target', size=24, fallback_size=True))
        point_btn.setIconSize(QSize(24, 24))
        point_btn.setToolTip("Add Point (+)")
        point_btn.setFixedSize(36, 36)
        point_btn.setStyleSheet(btn_style)
        point_btn.clicked.connect(lambda: self.view.set_drawing_mode("Add Point (+)"))
        point_label = QLabel("Point")
        point_label.setStyleSheet(label_style)
        drawing_grid.addWidget(point_btn, 0, 0, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        drawing_grid.addWidget(point_label, 0, 1, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        
        # --- Tool: Draw Line ---
        line_btn = QPushButton()
        line_btn.setIcon(fugue.icon('pencil', size=24, fallback_size=True))
        line_btn.setIconSize(QSize(24, 24))
        line_btn.setToolTip("Draw Line")
        line_btn.setFixedSize(36, 36)
        line_btn.setStyleSheet(btn_style)
        line_btn.clicked.connect(lambda: self.view.set_drawing_mode("Draw Line"))
        line_label = QLabel("Line")
        line_label.setStyleSheet(label_style)
        drawing_grid.addWidget(line_btn, 1, 0, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        drawing_grid.addWidget(line_label, 1, 1, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        
        # --- Tool: Draw Trajectory ---
        trajectory_btn = QPushButton()
        trajectory_btn.setIcon(fugue.icon('arrow-move', size=24, fallback_size=True))
        trajectory_btn.setIconSize(QSize(24, 24))
        trajectory_btn.setToolTip("Draw Trajectory - Select a unit first, then click this button")
        trajectory_btn.setFixedSize(36, 36)
        trajectory_btn.setStyleSheet(btn_style)
        trajectory_btn.clicked.connect(lambda: self._activate_trajectory_mode())
        trajectory_label = QLabel("Trajectory")
        trajectory_label.setStyleSheet(label_style)
        drawing_grid.addWidget(trajectory_btn, 2, 0, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        drawing_grid.addWidget(trajectory_label, 2, 1, alignment=Qt.AlignLeft | Qt.AlignVCenter)

        # --- Debug Button (for testing) ---
        debug_btn = QPushButton()
        debug_btn.setIcon(fugue.icon('bug', size=24, fallback_size=True))
        debug_btn.setIconSize(QSize(24, 24))
        debug_btn.setToolTip("Debug Selection State")
        debug_btn.setFixedSize(36, 36)
        debug_btn.setStyleSheet(btn_style)
        debug_btn.clicked.connect(lambda: self.view.debug_selection_state())
        debug_label = QLabel("Debug")
        debug_label.setStyleSheet(label_style)
        drawing_grid.addWidget(debug_btn, 2, 2, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        drawing_grid.addWidget(debug_label, 2, 3, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        
        # --- Tool: Text Tool (A) ---
        text_btn = QPushButton()
        text_btn.setIcon(fugue.icon('document-text', size=24, fallback_size=True))
        text_btn.setIconSize(QSize(24, 24))
        text_btn.setToolTip("Text Tool (A)")
        text_btn.setFixedSize(36, 36)
        text_btn.setStyleSheet(btn_style)
        text_btn.clicked.connect(lambda: self.view.set_drawing_mode("Add Text (A)"))
        text_label = QLabel("Text")
        text_label.setStyleSheet(label_style)
        drawing_grid.addWidget(text_btn, 3, 0, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        drawing_grid.addWidget(text_label, 3, 1, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        
        # --- Tool: Select Tool ---
        select_btn = QPushButton()
        select_btn.setIcon(fugue.icon('selection', size=24, fallback_size=True))
        select_btn.setIconSize(QSize(24, 24))
        select_btn.setToolTip("Select Tool")
        select_btn.setFixedSize(36, 36)
        select_btn.setStyleSheet(btn_style)
        select_btn.clicked.connect(lambda: self.view.set_drawing_mode("Select Tool"))
        select_label = QLabel("Select")
        select_label.setStyleSheet(label_style)
        drawing_grid.addWidget(select_btn, 4, 0, alignment=Qt.AlignLeft | Qt.AlignVCenter)
        drawing_grid.addWidget(select_label, 4, 1, alignment=Qt.AlignLeft | Qt.AlignVCenter)

        drawing_group.setLayout(drawing_grid)
        layout.addWidget(drawing_group)
        
        # Style Options Group (unchanged)
        style_group = QGroupBox("Style Options")
        style_layout = QVBoxLayout()
        color_layout = QHBoxLayout()
        color_label = QLabel("Color:")
        self.color_button = QPushButton()
        self.color_button.setFixedSize(30, 30)
        self.current_color = QColor(Qt.blue)  # Default color is now blue
        self.update_color_button()
        self.color_button.setIcon(fugue.icon('color', size=20, fallback_size=True))
        self.color_button.setToolTip("Choose Color")
        self.color_button.clicked.connect(self.choose_drawing_color)
        color_layout.addWidget(color_label)
        color_layout.addWidget(self.color_button)
        style_layout.addLayout(color_layout)
        size_layout = QHBoxLayout()
        size_label = QLabel("Size:")
        self.size_combo = QComboBox()
        px_sizes = [2, 4, 6, 8, 12, 16, 20, 24, 28, 32, 36, 40]
        self.size_combo.addItems([f"{v} px" for v in px_sizes])
        self.size_combo.setCurrentText("8 px")  # Default size is now 8 px
        self.size_combo.currentTextChanged.connect(self.update_drawing_size)
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.size_combo)
        style_layout.addLayout(size_layout)
        style_group.setLayout(style_layout)
        layout.addWidget(style_group)
        
        # Undo/Remove Group (unchanged)
        edit_group = QGroupBox("Edit Actions")
        edit_layout = QVBoxLayout()
        undo_btn = QPushButton("Undo Last Draw")
        undo_btn.setIcon(fugue.icon('arrow-curve-180-left', size=24, fallback_size=True))
        undo_btn.setIconSize(QSize(24, 24))
        undo_btn.setToolTip("Undo Last Draw")
        undo_btn.clicked.connect(self.view.undo_last_draw)
        edit_layout.addWidget(undo_btn)
        remove_btn = QPushButton("Remove Selected Draw")
        remove_btn.setIcon(fugue.icon('eraser', size=24, fallback_size=True))
        remove_btn.setIconSize(QSize(24, 24))
        remove_btn.setToolTip("Remove Selected Draw")
        remove_btn.clicked.connect(self.view.remove_selected_draw)
        edit_layout.addWidget(remove_btn)
        edit_group.setLayout(edit_layout)
        layout.addWidget(edit_group)
        
        # Coordinates Display Group (unchanged)
        coords_group = QGroupBox("Coordinates")
        coords_layout = QVBoxLayout()
        self.coords_label = QLabel("Current Position: Not set")
        self.coords_label.setStyleSheet("color: blue;")
        self.distance_label = QLabel("Distance: 0.0 meters")
        self.distance_label.setStyleSheet("color: green;")
        coords_layout.addWidget(self.coords_label)
        coords_layout.addWidget(self.distance_label)
        coords_group.setLayout(coords_layout)
        layout.addWidget(coords_group)
        
        widget.setLayout(layout)
        palette.setWidget(widget)
        return palette

    def update_color_button(self):
        """Updates the color button appearance with the current color."""
        pixmap = QPixmap(30, 30)
        pixmap.fill(self.current_color)
        self.color_button.setIcon(QIcon(pixmap))

    def choose_drawing_color(self):
        """Opens a color dialog and updates the current drawing color."""
        color = QColorDialog.getColor(self.current_color, self)
        if color.isValid():
            self.current_color = color
            self.update_color_button()
            if hasattr(self, 'view'):
                self.view.set_drawing_color(color)

    def update_drawing_size(self, size_text):
        """Updates the drawing size based on combo box selection and sets text size for text tool."""
        try:
            size = int(size_text.split()[0])
        except Exception:
            size = 16  # Default fallback
        if hasattr(self, 'view'):
            self.view.set_drawing_size(size)
            # Also set text size for text tool if available
            if hasattr(self.view, 'set_text_size'):
                self.view.set_text_size(size)

    def mouseMoveEvent(self, event):
        """Handle mouse movement to update coordinates."""
        super().mouseMoveEvent(event)
        if self.active_background_type == 'geotiff' and self.geotiff_ds:
            # Use the view's mapToScene method
            scene_pos = self.view.mapToScene(event.pos())
            xyz = self.get_xyz_from_scene_point(scene_pos)
            if xyz:
                lon, lat, z = xyz
                z_str = f"{z:.2f}" if not np.isnan(z) else "NaN"
                self.coords_label.setText(f"Position: Lon={lon:.6f}, Lat={lat:.6f}, Z={z_str}")
                
                # If drawing, calculate distance
                if hasattr(self.view, 'start_point') and self.view.start_point:
                    start_xyz = self.get_xyz_from_scene_point(self.view.start_point)
                    if start_xyz:
                        distance = self.calculate_distance_3d(start_xyz, xyz)
                        self.distance_label.setText(f"Distance: {distance:.2f} meters")

    def add_live_object_to_palette(self, obj):
        print(f"[DEBUG] add_live_object_to_palette - Adding object: {obj.name if hasattr(obj, 'name') else 'Unknown'}")
        try:
            # Create top-level item with object name (already has unique letter)
            top_item = QTreeWidgetItem([obj.name])
            print(f"[DEBUG] Created top item with name: {obj.name}")
            top_item.setData(0, Qt.UserRole, obj)
            print("[DEBUG] Stored object reference in item data")
            self.live_objects_list.addTopLevelItem(top_item)

            # Create child items with labels and inputs
            fields = [
                ("Name:", obj.name, "name"),
                ("Designation (lib 1):", getattr(obj, 'designation', ''), "designation"),
                ("Matériel (lib 2):", obj.object_type, "materiel"),
                ("Parent (lib 3):", getattr(obj, 'parent', ''), "parent"),
            ]
            # Add general info fields
            for label, value, field_name in fields:
                print(f"[DEBUG] Creating field: {field_name} with value: {value}")
                field_item = QTreeWidgetItem()
                top_item.addChild(field_item)
                widget = QWidget()
                layout = QHBoxLayout(widget)
                layout.setContentsMargins(1, 1, 1, 1)
                layout.setSpacing(2)
                label_widget = QLabel(label)
                label_widget.setStyleSheet("""
                    QLabel {
                        color: #2c3e50;
                        font-size: 8pt;
                        padding: 2px;
                    }
                """)
                edit = QLineEdit(str(value))
                edit.setFixedHeight(20)
                edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 8pt;
                        padding: 2px 4px;
                        border: 1px solid #bdc3c7;
                        border-radius: 2px;
                        background: white;
                    }
                    QLineEdit:focus {
                        border: 1px solid #3498db;
                        background: #f8f9fa;
                    }
                """)
                if field_name == "name":
                    edit.setReadOnly(True)
                    edit.setStyleSheet(edit.styleSheet() + """
                        QLineEdit:read-only {
                            background: #f5f6f7;
                            color: #7f8c8d;
                        }
                    """)
                setattr(obj, f"{field_name}_edit", edit)
                edit.editingFinished.connect(
                    lambda obj=obj, edit=edit, field=field_name: 
                    self.save_field_value(obj, edit, field)
                )
                layout.addWidget(label_widget)
                layout.addWidget(edit)
                self.live_objects_list.setItemWidget(field_item, 0, widget)

            # --- Portée Group (AP, AC, Missile) ---
            # Add a horizontal line for visual separation
            line_item = QTreeWidgetItem()
            top_item.addChild(line_item)
            line_widget = QFrame()
            line_widget.setFrameShape(QFrame.HLine)
            line_widget.setFrameShadow(QFrame.Sunken)
            line_widget.setStyleSheet("margin-top: 6px; margin-bottom: 6px;")
            self.live_objects_list.setItemWidget(line_item, 0, line_widget)

            # Group portée fields in a vertical layout
            portee_fields = [
                ("AP (m):", str(getattr(obj, 'ap_radius_m', 400)), "ap_radius_m"),
                ("AC (m):", str(getattr(obj, 'ac_radius_m', 800)), "ac_radius_m"),
                ("Missile (m):", str(getattr(obj, 'missile_radius_m', 1600)), "missile_radius_m"),
            ]
            portee_group_item = QTreeWidgetItem()
            top_item.addChild(portee_group_item)
            portee_group_widget = QWidget()
            portee_group_layout = QVBoxLayout(portee_group_widget)
            portee_group_layout.setContentsMargins(1, 1, 1, 1)
            portee_group_layout.setSpacing(2)
            # Add title label
            title_label = QLabel("Portée (AP, AC, Missile)")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 10pt; margin-bottom: 4px;")
            portee_group_layout.addWidget(title_label)
            for label, value, field_name in portee_fields:
                row = QHBoxLayout()
                label_widget = QLabel(label)
                label_widget.setStyleSheet("color: #2c3e50; font-size: 8pt; padding: 2px;")
                edit = QLineEdit(str(value))
                edit.setFixedHeight(20)
                edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 8pt;
                        padding: 2px 4px;
                        border: 1px solid #bdc3c7;
                        border-radius: 2px;
                        background: white;
                    }
                    QLineEdit:focus {
                        border: 1px solid #3498db;
                        background: #f8f9fa;
                    }
                """)
                setattr(obj, f"{field_name}_edit", edit)
                def make_update_radius(field, obj=obj, edit=edit):
                    def handler():
                        try:
                            val = float(edit.text())
                            setattr(obj, field, val)
                            if field == "ap_radius_m":
                                obj.set_ap_radius_by_meters(val)
                            elif field == "ac_radius_m":
                                obj.set_ac_radius_by_meters(val)
                            elif field == "missile_radius_m":
                                obj.set_missile_radius_by_meters(val)
                            print(f"[DEBUG] Updated {field} for {obj.name} to {val}")
                        except Exception as e:
                            print(f"[ERROR] Invalid value for {field}: {edit.text()} - {e}")
                    return handler
                edit.editingFinished.connect(make_update_radius(field_name))
                row.addWidget(label_widget)
                row.addWidget(edit)
                portee_group_layout.addLayout(row)
            self.live_objects_list.setItemWidget(portee_group_item, 0, portee_group_widget)

            # --- Health/Counts Group ---
            # Add a horizontal line for visual separation
            health_line_item = QTreeWidgetItem()
            top_item.addChild(health_line_item)
            health_line_widget = QFrame()
            health_line_widget.setFrameShape(QFrame.HLine)
            health_line_widget.setFrameShadow(QFrame.Sunken)
            health_line_widget.setStyleSheet("margin-top: 6px; margin-bottom: 6px;")
            self.live_objects_list.setItemWidget(health_line_item, 0, health_line_widget)

            # Group personnel/vehicle/mechanized/tank fields in a vertical layout
            health_group_item = QTreeWidgetItem()
            top_item.addChild(health_group_item)
            health_group_widget = QWidget()
            health_group_layout = QVBoxLayout(health_group_widget)
            health_group_layout.setContentsMargins(1, 1, 1, 1)
            health_group_layout.setSpacing(2)
            # Add title label
            health_title_label = QLabel("Unit Composition")
            health_title_label.setAlignment(Qt.AlignCenter)
            health_title_label.setStyleSheet("font-weight: bold; font-size: 10pt; margin-bottom: 4px;")
            health_group_layout.addWidget(health_title_label)
            # Add fields
            count_fields = [
                ("Personnel:", str(getattr(obj, 'personnel_count', 0)), "personnel_count"),
                ("Jeep:", str(getattr(obj, 'vehicle_count', 0)), "vehicle_count"),
                ("M113:", str(getattr(obj, 'mechanized_count', 0)), "mechanized_count"),
                ("Tank:", str(getattr(obj, 'tank_count', 0)), "tank_count"),
            ]
            # FINALIZE INIT HEALTH after all counts are set
            if hasattr(obj, 'finalize_init_health'):
                obj.finalize_init_health()
            self._health_bar_widgets = getattr(self, '_health_bar_widgets', {})
            def update_health_bar():
                max_health = 1
                # For now, use a fixed max_health for the bar (or calculate based on initial values)
                try:
                    max_health = (
                        int(obj.personnel_count) * 1 +
                        int(obj.vehicle_count) * 15 +
                        int(obj.mechanized_count) * 30 +
                        int(obj.tank_count) * 60
                    )
                except Exception:
                    max_health = 1
                health = max_health
                if hasattr(obj, 'total_health'):
                    health = obj.total_health()
                if health_bar:
                    health_bar.setMaximum(max_health if max_health > 0 else 1)
                    health_bar.setValue(health)
                    health_bar.setFormat(f"Health: {health}/{max_health}")
            for label, value, field_name in count_fields:
                row = QHBoxLayout()
                label_widget = QLabel(label)
                label_widget.setStyleSheet("color: #2c3e50; font-size: 8pt; padding: 2px;")
                edit = QLineEdit(str(value))
                edit.setFixedHeight(20)
                edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 8pt;
                        padding: 2px 4px;
                        border: 1px solid #bdc3c7;
                        border-radius: 2px;
                        background: white;
                    }
                    QLineEdit:focus {
                        border: 1px solid #3498db;
                        background: #f8f9fa;
                    }
                """)
                setattr(obj, f"{field_name}_edit", edit)
                def make_update_count(field, obj=obj, edit=edit):
                    def handler():
                        try:
                            val = int(edit.text())
                            setattr(obj, field, val)
                            update_health_bar()
                        except Exception as e:
                            print(f"[ERROR] Invalid value for {field}: {edit.text()} - {e}")
                    return handler
                edit.editingFinished.connect(make_update_count(field_name))
                row.addWidget(label_widget)
                row.addWidget(edit)
                health_group_layout.addLayout(row)
            # Add health bar
            health_bar = QProgressBar()
            health_bar.setMinimum(0)
            health_bar.setMaximum(1)
            health_bar.setValue(1)
            health_bar.setTextVisible(True)
            health_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 2px;
                    background: #f5f6f7;
                    height: 18px;
                    font-size: 8pt;
                }
                QProgressBar::chunk {
                    background-color: #27ae60;
                    border-radius: 2px;
                }
            """)
            health_group_layout.addWidget(health_bar)
            self._health_bar_widgets[obj] = health_bar
            update_health_bar()
            self.live_objects_list.setItemWidget(health_group_item, 0, health_group_widget)

            # --- Children Section ---
            children_item = QTreeWidgetItem(["Children:"])
            top_item.addChild(children_item)
            children_widget = QWidget()
            children_layout = QVBoxLayout(children_widget)
            children_layout.setContentsMargins(1, 1, 1, 1)
            children_layout.setSpacing(2)
            # Dropdown for possible children
            child_combo = QComboBox()
            child_combo.addItems(self.possible_child_names)
            children_layout.addWidget(child_combo)
            # Add Child button
            add_child_btn = QPushButton("Add Child")
            children_layout.addWidget(add_child_btn)
            # List of assigned children
            assigned_children_list = QListWidget()
            children_layout.addWidget(assigned_children_list)
            self.live_objects_list.setItemWidget(children_item, 0, children_widget)
            # Store children list on obj for later update
            obj._assigned_children_list_widget = assigned_children_list
            # Add handler for Add Child
            def handle_add_child():
                child_name = child_combo.currentText()
                if not hasattr(obj, 'children'):
                    obj.children = []
                obj.children.append(child_name)
                assigned_children_list.addItem(child_name)
                self.create_child_marker_and_line(obj, child_name)
            add_child_btn.clicked.connect(handle_add_child)
            # If already has children, show them
            if hasattr(obj, 'children'):
                for cname in obj.children:
                    assigned_children_list.addItem(cname)
            # Add to live objects list
            self.live_objects.append(obj)
            print(f"[DEBUG] Successfully added {obj.name} to live objects list")
            top_item.setExpanded(False)
            # Show the Live Objects dock if at least one object exists
            if hasattr(self, 'live_objects_dock'):
                if len(self.live_objects) > 0:
                    self.live_objects_dock.show()
            # Show the Portée Control panel if at least one object exists
            if hasattr(self, 'palette') and hasattr(self.palette, 'portee_group'):
                print(f"[DEBUG] add_live_object_to_palette: live_objects count = {len(self.live_objects)}")
                if len(self.live_objects) > 0:
                    print("[DEBUG] Showing Portée Control panel")
                    self.palette.portee_group.setVisible(True)
        except Exception as e:
            print(f"[ERROR] Error in add_live_object_to_palette: {str(e)}")
            import traceback
            traceback.print_exc()

    def save_field_value(self, obj, edit, field):
        """Save the edited field value and update the display"""
        print(f"[DEBUG] Saving field value - Field: {field}, Value: {edit.text()}")
        value = edit.text()
        
        try:
            # Store the value in both the edit field and as a property
            setattr(obj, f"{field}_edit", edit)
            setattr(obj, field, value)
            print(f"[DEBUG] Successfully stored value for {field}")
            
            # Update the object's label if designation changed
            if field == "designation":
                print(f"[DEBUG] Calling set_label_text on obj with value: {value}")
                obj.set_label_text(value)
            
            # Update info labels on map
            self.remove_all_info_labels()
            self.show_info_labels(obj)
        except Exception as e:
            print(f"[ERROR] Failed to save field value: {str(e)}")

    def setup_tree_widget_connections(self):
        """Setup all tree widget signal connections"""
        print("[DEBUG] Setting up tree widget connections")
        try:
            # Disconnect any existing connections first to avoid duplicates
            try:
                self.live_objects_list.itemClicked.disconnect()
            except:
                pass
                
            # Connect with proper lambda to ensure item is passed
            self.live_objects_list.itemClicked.connect(self.on_item_clicked)
            print("[DEBUG] Successfully connected tree widget signals")
        except Exception as e:
            print(f"[ERROR] Error setting up tree widget connections: {str(e)}")
            import traceback
            traceback.print_exc()

    def show_object_info_panel(self, obj):
        self.selected_object = obj
        self.object_info_panel.setVisible(True)
        self.info_designation.setText(obj.name)
        self.info_materiel.setText(obj.object_type)
        self.info_parent.setText(getattr(obj, 'parent', ''))

    def save_object_info(self):
        if not self.selected_object:
            return
        self.selected_object.name = self.info_designation.text()
        self.selected_object.object_type = self.info_materiel.text()
        self.selected_object.parent = self.info_parent.text()
        # Update the label in the list
        for i in range(self.live_objects_list.topLevelItemCount()):
            item = self.live_objects_list.topLevelItem(i)
            if item.data(0, Qt.UserRole) == self.selected_object:
                item.setText(0, self.selected_object.name)
                break
        
        # Update info labels on map
        main_window = self.main_window
        if hasattr(main_window, 'remove_all_info_labels') and hasattr(main_window, 'show_info_labels'):
            main_window.remove_all_info_labels()
            main_window.show_info_labels(self.selected_object)
            
    def sync_map_selection_to_palette(self):
        selected_items = self.view.scene.selectedItems()
        self.remove_all_info_labels()
        self.object_info_panel.setVisible(False)
        if not selected_items:
            self.live_objects_list.clearSelection()
            return
        for item in selected_items:
            obj = self.find_object_by_graphic(item)
            if obj:
                for i in range(self.live_objects_list.topLevelItemCount()):
                    list_item = self.live_objects_list.topLevelItem(i)
                    if list_item.data(0, Qt.UserRole) == obj:
                        self.live_objects_list.setCurrentItem(list_item)
                        self.show_info_labels(obj)
                        self.show_object_info_panel(obj)
                        return
        self.live_objects_list.clearSelection()

    def show_info_labels(self, obj):
        """Show info labels around the object on the map"""
        print(f"[DEBUG] Showing info labels for object: {obj.name if hasattr(obj, 'name') else 'Unknown'}")
        
        try:
            if not hasattr(obj, 'graphic'):
                print("[DEBUG] Object has no graphic, skipping labels")
                return
                
            # Remove old labels first
            self.remove_all_info_labels()
            
            pos = obj.graphic.pos()
            labels = []
            
            # Get values from edit fields
            designation = getattr(obj, 'designation_edit', None)
            materiel = getattr(obj, 'materiel_edit', None)
            parent = getattr(obj, 'parent_edit', None)
            
            print(f"[DEBUG] Retrieved field values - Designation: {designation.text() if designation else 'None'}")
            
            # Add designation number if it exists
            if designation and designation.text():
                print(f"[DEBUG] Creating designation label: {designation.text()}")
                label = QGraphicsTextItem(designation.text())
                label.setDefaultTextColor(Qt.black)
                label.setPos(pos.x() - 10, pos.y() - 25)
                labels.append(label)
            
            # Add other info
            info_text = []
            if materiel and materiel.text():
                info_text.append(materiel.text())
            if parent and parent.text():
                info_text.append(f"Parent: {parent.text()}")
            
            if info_text:
                print(f"[DEBUG] Creating info label with text: {info_text}")
                info_label = QGraphicsTextItem('\n'.join(info_text))
                info_label.setDefaultTextColor(Qt.black)
                info_label.setPos(pos.x() + 25, pos.y() + 10)
                labels.append(info_label)
            
            # Add labels to scene and store references
            for label in labels:
                print("[DEBUG] Adding label to scene")
                self.view.scene.addItem(label)
                if obj not in self.info_labels:
                    self.info_labels[obj] = []
                self.info_labels[obj].append(label)
                
            print(f"[DEBUG] Successfully added {len(labels)} labels")
        except Exception as e:
            print(f"[ERROR] Error in show_info_labels: {str(e)}")

    def remove_info_labels(self, obj):
        """Remove info labels for a specific object."""
        if obj in self.info_labels:
            for label in self.info_labels[obj]:
                self.view.scene.removeItem(label)
            del self.info_labels[obj]

    def remove_all_info_labels(self):
        """Remove all info labels from the map"""
        print("[DEBUG] Removing all info labels")
        try:
            for obj, labels in self.info_labels.items():
                for label in labels:
                    if label in self.view.scene.items():
                        self.view.scene.removeItem(label)
            self.info_labels.clear()
            print("[DEBUG] Successfully removed all labels")
        except Exception as e:
            print(f"[ERROR] Error in remove_all_info_labels: {str(e)}")

    def select_object_on_map(self, item):
        obj = item.data(0, Qt.UserRole)
        if obj and hasattr(obj, 'graphic'):
            self.view.scene.clearSelection()
            obj.graphic.setSelected(True)
            # self.view.centerOn(obj.graphic)  # Removed to prevent map jump

    def remove_live_object_from_palette(self, obj):
        """Remove the object's entry from the live_objects_list and live_objects, and free its letter."""
        print(f"[DEBUG] remove_live_object_from_palette called for: {getattr(obj, 'name', obj)}")
        # Remove from live_objects_list
        for i in range(self.live_objects_list.topLevelItemCount()):
            item = self.live_objects_list.topLevelItem(i)
            if item.data(0, Qt.UserRole) == obj:
                self.live_objects_list.takeTopLevelItem(i)
                break
        # Remove from live_objects
        if obj in self.live_objects:
            self.live_objects.remove(obj)
        # Free up the letter for reuse
        base_type = obj.object_type
        idx = getattr(obj, '_letter_idx', None)
        if idx is not None and base_type in self.object_type_letter_used:
            self.object_type_letter_used[base_type].discard(idx)
        # Remove child markers/lines from map
        if obj in self.child_graphics:
            for marker, line in self.child_graphics[obj]:
                self.view.scene.removeItem(marker)
                self.view.scene.removeItem(line)
            del self.child_graphics[obj]
        # Hide the Live Objects dock if no objects remain
        if hasattr(self, 'live_objects_dock'):
            if len(self.live_objects) == 0:
                self.live_objects_dock.hide()
        # Hide the Portée Control panel if no objects remain
        if hasattr(self, 'palette') and hasattr(self.palette, 'portee_group'):
            print(f"[DEBUG] remove_live_object_from_palette: live_objects count = {len(self.live_objects)}")
            if len(self.live_objects) == 0:
                print("[DEBUG] Hiding Portée Control panel")
                self.palette.portee_group.setVisible(False)
        self.update_health_bar_panel_visibility()

    def save_field_value(self, obj, edit, field):
        """Save the edited field value and update the display"""
        print(f"[DEBUG] Saving field value - Field: {field}, Value: {edit.text()}")
        value = edit.text()
        
        try:
            # Store the value in both the edit field and as a property
            setattr(obj, f"{field}_edit", edit)
            setattr(obj, field, value)
            print(f"[DEBUG] Successfully stored value for {field}")
            
            # Update the object's label if designation changed
            if field == "designation":
                print(f"[DEBUG] Calling set_label_text on obj with value: {value}")
                obj.set_label_text(value)
            
            # Update info labels on map
            self.remove_all_info_labels()
            self.show_info_labels(obj)
        except Exception as e:
            print(f"[ERROR] Failed to save field value: {str(e)}")

    def get_next_letter_for_type(self, object_type):
        """Return the lowest available uppercase letter for this object type."""
        used = self.object_type_letter_used.setdefault(object_type, set())
        idx = 0
        while idx in used:
            idx += 1
        used.add(idx)
        letter = chr(ord('A') + idx)
        return letter, idx

    def create_child_marker_and_line(self, parent_obj, child_name):
        # Find parent position
        parent_pos = parent_obj.graphic.pos()
        # Offset for child (stacked if multiple)
        n = len(parent_obj.children)
        offset_x = 60
        offset_y = (n - 1) * 40
        child_pos = QPointF(parent_pos.x() + offset_x, parent_pos.y() + offset_y)
        # Create child marker
        marker = QGraphicsRectItem(-20, -20, 40, 40)
        marker.setBrush(QBrush(QColor("yellow")))
        marker.setPen(QPen(QColor("black")))
        marker.setPos(child_pos)
        text = QGraphicsTextItem(child_name, marker)
        text.setDefaultTextColor(QColor("black"))
        text.setPos(-15, -10)
        self.view.scene.addItem(marker)
        # Draw line from parent to child
        line = QGraphicsLineItem(parent_pos.x(), parent_pos.y(), child_pos.x(), child_pos.y())
        line.setPen(QPen(QColor("red"), 2))
        self.view.scene.addItem(line)
        # Track for cleanup
        if parent_obj not in self.child_graphics:
            self.child_graphics[parent_obj] = []
        self.child_graphics[parent_obj].append((marker, line))


    def play_scene_sequence(self):
        """🎬 NEW CLEAN TRAJECTORY PLAYBACK SYSTEM"""
        print("🎬 PLAY TRAJECTORIES: Starting new clean playback...")

        # Check if we have any trajectories in our NEW system
        if not self.unit_trajectories:
            print("❌ No trajectories found in NEW system!")
            QMessageBox.warning(self, "Warning", "No trajectories to play. Draw some trajectories first!")
            return

        print(f"✅ Found {len(self.unit_trajectories)} trajectories to play:")
        for unit_name, trajectory in self.unit_trajectories.items():
            print(f"   📍 {unit_name}: {len(trajectory)} points")

        # Start the animation
        self.animation_in_progress = True
        self.animation_paused = False
        self.current_animation_group = QParallelAnimationGroup(self)

        # Create animations for each unit with a trajectory
        for unit_name, trajectory in self.unit_trajectories.items():
            # Find the unit object
            unit_obj = next((obj for obj in self.objects if obj.name == unit_name), None)
            if not unit_obj:
                print(f"⚠️ Unit {unit_name} not found in objects list")
                continue

            if len(trajectory) < 2:
                print(f"⚠️ Unit {unit_name} trajectory too short ({len(trajectory)} points)")
                continue

            print(f"🎯 Creating animation for {unit_name} with {len(trajectory)} points")

            # Create sequential animation for this unit
            unit_animation = QSequentialAnimationGroup()

            # Create animation segments between each pair of points
            for i in range(len(trajectory) - 1):
                start_point = trajectory[i]
                end_point = trajectory[i + 1]

                # Calculate distance and duration
                distance_pixels = ((end_point.x() - start_point.x()) ** 2 +
                                 (end_point.y() - start_point.y()) ** 2) ** 0.5

                # Base duration (you can adjust this)
                base_duration = max(500, int(distance_pixels * 10))  # Minimum 500ms
                final_duration = int(base_duration / self.speed_multiplier) if self.speed_multiplier > 0 else base_duration

                # Create the animation
                wrapper = GraphicsItemAnimationWrapper(unit_obj.graphic)
                animation = QPropertyAnimation(wrapper, b"pos")
                animation.setDuration(final_duration)
                animation.setStartValue(start_point)
                animation.setEndValue(end_point)

                unit_animation.addAnimation(animation)

            # Add this unit's animation to the parallel group
            self.current_animation_group.addAnimation(unit_animation)

        # Connect finished signal and start
        self.current_animation_group.finished.connect(self.on_sequence_finished)
        self.current_animation_group.start()
        self.sequence_state = 'Playing'
        print("🚀 NEW TRAJECTORY PLAYBACK STARTED!")

    def on_sequence_finished(self):
        print("Sequence finished. Resetting to initial state.")
        self.sequence_is_playing = False
        self.animation_in_progress = False
        print(f"[DEBUG][SEQ] sequence_is_playing = {self.sequence_is_playing}, animation_in_progress = {self.animation_in_progress}")
        self.animation_paused = False
        self.sequence_state = 'Finished'

    def play_powerpoint_sequence(self, animation_properties):
        """🎬 PowerPoint-style animation sequence with triggers"""
        print("🎬 POWERPOINT SEQUENCE: Starting advanced playback...")

        if not animation_properties:
            print("❌ No animation properties found!")
            return

        # Sort animations by order
        sorted_animations = sorted(animation_properties.values(), key=lambda x: x.order)
        enabled_animations = [anim for anim in sorted_animations if anim.enabled]

        if not enabled_animations:
            print("❌ No enabled animations found!")
            return

        print(f"✅ Found {len(enabled_animations)} enabled animations:")
        for anim in enabled_animations:
            print(f"   {anim.order + 1}. {anim.unit_name} - {anim.trigger.value} - {anim.duration}s")

        # Start the animation sequence
        self.animation_in_progress = True
        self.animation_paused = False
        self.sequence_state = 'Playing'
        self.current_powerpoint_animations = enabled_animations
        self.current_animation_index = 0

        # Start the first animation or group of animations
        self.start_next_powerpoint_animation_group()

    def start_next_powerpoint_animation_group(self):
        """Start the next group of animations based on triggers"""
        if self.current_animation_index >= len(self.current_powerpoint_animations):
            # All animations finished
            self.on_powerpoint_sequence_finished()
            return

        # Collect animations that should start together
        current_group = []
        start_index = self.current_animation_index

        # Add the first animation
        current_group.append(self.current_powerpoint_animations[start_index])
        self.current_animation_index += 1

        # Check if subsequent animations should start "With Previous"
        while (self.current_animation_index < len(self.current_powerpoint_animations) and
               self.current_powerpoint_animations[self.current_animation_index].trigger == AnimationTrigger.WITH_PREVIOUS):
            current_group.append(self.current_powerpoint_animations[self.current_animation_index])
            self.current_animation_index += 1

        print(f"🎬 Starting animation group with {len(current_group)} animations:")
        for anim in current_group:
            print(f"   - {anim.unit_name} ({anim.trigger.value})")

        # Create and start the animation group
        self.create_and_start_animation_group(current_group)

    def create_and_start_animation_group(self, animation_group):
        """Create and start a group of animations"""
        self.current_animation_group = QParallelAnimationGroup(self)

        for anim_props in animation_group:
            # Find the unit object
            unit_obj = next((obj for obj in self.objects if obj.name == anim_props.unit_name), None)
            if not unit_obj:
                print(f"⚠️ Unit {anim_props.unit_name} not found in objects list")
                continue

            if len(anim_props.trajectory_points) < 2:
                print(f"⚠️ Unit {anim_props.unit_name} trajectory too short")
                continue

            print(f"🎯 Creating PowerPoint animation for {anim_props.unit_name}")

            # Create sequential animation for this unit
            unit_animation = QSequentialAnimationGroup()

            # Add delay if specified
            if anim_props.delay > 0:
                # Use wrapper for delay animation too
                delay_wrapper = GraphicsItemAnimationWrapper(unit_obj.graphic)
                delay_animation = QPropertyAnimation(delay_wrapper, b"pos")
                delay_animation.setDuration(int(anim_props.delay * 1000))
                delay_animation.setStartValue(unit_obj.graphic.pos())
                delay_animation.setEndValue(unit_obj.graphic.pos())
                unit_animation.addAnimation(delay_animation)

            # Create trajectory animation
            trajectory_animation = self.create_trajectory_animation(unit_obj, anim_props)
            unit_animation.addAnimation(trajectory_animation)

            # Add to parallel group
            self.current_animation_group.addAnimation(unit_animation)

        # Connect finished signal and start
        self.current_animation_group.finished.connect(self.on_animation_group_finished)
        self.current_animation_group.start()
        print("🚀 PowerPoint animation group started!")

    def create_trajectory_animation(self, unit_obj, anim_props):
        """Create trajectory animation with specified properties"""
        trajectory = anim_props.trajectory_points

        # Create sequential animation for trajectory segments
        trajectory_animation = QSequentialAnimationGroup()

        # Calculate total distance for timing
        total_distance = 0
        for i in range(len(trajectory) - 1):
            start_point = trajectory[i]
            end_point = trajectory[i + 1]
            distance = ((end_point.x() - start_point.x()) ** 2 +
                       (end_point.y() - start_point.y()) ** 2) ** 0.5
            total_distance += distance

        # Create animation segments
        total_duration_ms = int(anim_props.duration * 1000)

        for i in range(len(trajectory) - 1):
            start_point = trajectory[i]
            end_point = trajectory[i + 1]

            # Calculate segment distance and duration
            segment_distance = ((end_point.x() - start_point.x()) ** 2 +
                              (end_point.y() - start_point.y()) ** 2) ** 0.5

            if total_distance > 0:
                segment_duration = int((segment_distance / total_distance) * total_duration_ms)
            else:
                segment_duration = total_duration_ms // (len(trajectory) - 1)

            segment_duration = max(100, segment_duration)  # Minimum 100ms per segment

            # Create the animation
            wrapper = GraphicsItemAnimationWrapper(unit_obj.graphic)
            animation = QPropertyAnimation(wrapper, b"pos")
            animation.setDuration(segment_duration)
            animation.setStartValue(start_point)
            animation.setEndValue(end_point)

            # Apply easing curve
            easing_map = {
                "Linear": QEasingCurve.Linear,
                "InQuad": QEasingCurve.InQuad,
                "OutQuad": QEasingCurve.OutQuad,
                "InOutQuad": QEasingCurve.InOutQuad,
                "InCubic": QEasingCurve.InCubic,
                "OutCubic": QEasingCurve.OutCubic,
                "InOutCubic": QEasingCurve.InOutCubic
            }
            easing_curve = easing_map.get(anim_props.easing, QEasingCurve.Linear)
            animation.setEasingCurve(easing_curve)

            trajectory_animation.addAnimation(animation)

        return trajectory_animation

    def on_animation_group_finished(self):
        """Handle animation group completion"""
        print("🎬 Animation group finished")

        # Check if there are more animations to play
        if self.current_animation_index < len(self.current_powerpoint_animations):
            next_anim = self.current_powerpoint_animations[self.current_animation_index]

            if next_anim.trigger == AnimationTrigger.AFTER_PREVIOUS:
                # Start next animation automatically
                print("🎬 Starting next animation (After Previous)")
                self.start_next_powerpoint_animation_group()
            elif next_anim.trigger == AnimationTrigger.CLICK:
                # Wait for user click
                print("🎬 Waiting for click to continue...")
                self.sequence_state = 'Paused'
                # The click will be handled by the main window's click event
            elif next_anim.trigger == AnimationTrigger.ON_TIME:
                # Start after specified delay
                print(f"🎬 Starting next animation after {next_anim.delay}s delay")
                QTimer.singleShot(int(next_anim.delay * 1000), self.start_next_powerpoint_animation_group)
        else:
            # All animations finished
            self.on_powerpoint_sequence_finished()

    def on_powerpoint_sequence_finished(self):
        """Handle PowerPoint sequence completion"""
        print("🎬 PowerPoint sequence finished!")
        self.sequence_is_playing = False
        self.animation_in_progress = False
        self.animation_paused = False
        self.sequence_state = 'Finished'

    def handle_powerpoint_click(self):
        """Handle click during PowerPoint sequence playback"""
        if (hasattr(self, 'current_powerpoint_animations') and
            hasattr(self, 'current_animation_index') and
            self.sequence_state == 'Paused'):

            if self.current_animation_index < len(self.current_powerpoint_animations):
                next_anim = self.current_powerpoint_animations[self.current_animation_index]
                if next_anim.trigger == AnimationTrigger.CLICK:
                    print("🎬 Click detected! Continuing PowerPoint sequence...")
                    self.sequence_state = 'Playing'
                    self.start_next_powerpoint_animation_group()
                    return True
        return False

    def reset_to_initial_positions(self):
        if not self.initial_positions:
            print("No initial positions stored")
            return
        initial_time = self.scenes[0]['time'] if self.scenes else "T1"
        for obj in self.objects:
            if obj.name in self.initial_positions:
                initial_pos = self.initial_positions[obj.name]
                obj.graphic.setPos(initial_pos['x'], initial_pos['y'])
                if hasattr(obj.graphic, 'setRotation'):
                    obj.graphic.setRotation(initial_pos['rotation'])
                self.update_object_image_for_time(obj, initial_time)
        # Restore health/state after animation
        for obj in self.objects:
            if hasattr(obj, 'reset_to_initial_state'):
                obj.reset_to_initial_state()
        self.update_live_object_health_bars()
        if hasattr(self, 'palette') and hasattr(self.palette, 'scenes_list'):
            self.palette.scenes_list.highlight_current_scene(-1)
        print(f"Objects reset to initial positions with time {initial_time}")
        # Centralized: Only set simulation_running False here
        if getattr(self, 'simulation_running', False):
            self.simulation_running = False
            print("[DEBUG][SIM] Exited Simulation Mode (simulation_running = False)")
            if hasattr(self, 'mode_button'):
                self.mode_button.setText("Switch to Simulation Mode")
                self.palette.setEnabled(True)

    def switch_mode(self):
        # Ensure mode_button exists and is in the UI
        if not hasattr(self, 'mode_button'):
            self.mode_button = QPushButton("Switch to Simulation Mode")
            self.mode_button.clicked.connect(self.switch_mode)
            if hasattr(self, 'centralWidget') and self.centralWidget():
                layout = self.centralWidget().layout()
                if layout and self.mode_button not in [layout.itemAt(i).widget() for i in range(layout.count()) if layout.itemAt(i).widget()]:
                    layout.addWidget(self.mode_button)
        if self.mode_button.text() == "Switch to Simulation Mode":
            self.play_scene_sequence()  # Start scene animation and simulation mode
            print("[SIM] Switched to Simulation Mode and started scene playback.")
        else:
            self.pause_scene_sequence()  # Pause scene animation
            print("[SIM] Switched to Editing Mode and paused scene playback.")

    def on_animation_finished(self, scene_index):
        self.current_scene_index = scene_index
        current_scene = self.scenes[scene_index]
        for obj in self.objects:
            if obj.name in current_scene['objects']:
                self.update_object_image_for_time(obj, current_scene['time'])
        self.current_animation_group = None
        self._animation_wrappers = []
        self.update_simulation()
        if scene_index >= len(self.scenes) - 1:
            self.animation_in_progress = False
            self.animation_paused = False
            # Do NOT set simulation_running = False here; only in reset_to_initial_positions
            self.sequence_state = 'Finished'
            self.play_sequence_btn.setText("Play Sequence")
            self.play_sequence_btn.setEnabled(True)
            QTimer.singleShot(500, self.reset_to_initial_positions)
            self.enable_scene_modifications()
            self.current_scene_index = -1
            if hasattr(self, 'palette') and hasattr(self.palette, 'sim_report_btn'):
                self.palette.sim_report_btn.setEnabled(True)
        else:
            if not self.animation_paused:
                self.sequence_state = 'Playing'
                QTimer.singleShot(1, self.animate_to_next_scene)
                self.force_enable_portee_controls()

    def pause_scene_sequence(self):
        if not getattr(self, 'sequence_is_playing', False) or getattr(self, 'animation_paused', False):
            return
        self.animation_paused = True
        if self.current_animation_group:
            self.current_animation_group.pause()
        self.sequence_state = 'Paused'
        self.force_enable_portee_controls()

    def pause_scene_sequence(self):
        if not getattr(self, 'sequence_is_playing', False) or getattr(self, 'animation_paused', False):
            return
        self.animation_paused = True
        if self.current_animation_group:
            self.current_animation_group.pause()
        self.sequence_state = 'Paused'
        self.force_enable_portee_controls()

    def force_enable_portee_controls(self):
        """Force enable Portée panel and all switches, with debug print."""
        if hasattr(self, 'palette') and hasattr(self.palette, 'portee_group'):
            if len(self.live_objects) > 0:
                self.palette.portee_group.setVisible(True)
                self.palette.portee_group.setEnabled(True)
                self.palette.visualiser_portee_switch.setEnabled(True)
                self.palette.portee1_switch.setEnabled(True)
                self.palette.portee2_switch.setEnabled(True)
                self.palette.portee3_switch.setEnabled(True)
                # print(f"[DEBUG] [force_enable_portee_controls] Portée panel and switches enabled: {self.palette.visualiser_portee_switch.isEnabled()}, {self.palette.portee1_switch.isEnabled()}, {self.palette.portee2_switch.isEnabled()}, {self.palette.portee3_switch.isEnabled()}")

    def capture_csra_overlay(self):
        """Capture the second monitor's screen at max quality, crop/scale to match PNG overlay if available, and load as overlay."""
        screens = QGuiApplication.screens()
        if len(screens) < 2:
            QMessageBox.warning(self, "No Second Monitor", "Second monitor not detected. Please connect a second monitor to use CSRA capture.")
            return False
        screen = screens[1]  # Second monitor
        # Capture at native resolution for max quality
        screenshot = screen.grabWindow(0)
        # If PNG overlay is loaded, crop/scale screenshot to match PNG overlay's aspect ratio and size
        png_rect = None
        png_image = None
        if hasattr(self, 'geotiff_png_pixmap_item') and self.geotiff_png_pixmap_item:
            # Get PNG overlay's size and aspect ratio
            png_pixmap = self.geotiff_png_pixmap_item.pixmap()
            png_image = png_pixmap.toImage()
            png_width = png_image.width()
            png_height = png_image.height()
            png_aspect = png_width / png_height if png_height else 1
            # Crop screenshot to match PNG aspect ratio (centered)
            shot_width = screenshot.width()
            shot_height = screenshot.height()
            shot_aspect = shot_width / shot_height if shot_height else 1
            if abs(shot_aspect - png_aspect) > 0.01:
                # Need to crop
                if shot_aspect > png_aspect:
                    # Screenshot is wider than PNG: crop width
                    new_width = int(shot_height * png_aspect)
                    x_offset = (shot_width - new_width) // 2
                    crop_rect = screenshot.copy(x_offset, 0, new_width, shot_height)
                else:
                    # Screenshot is taller than PNG: crop height
                    new_height = int(shot_width / png_aspect)
                    y_offset = (shot_height - new_height) // 2
                    crop_rect = screenshot.copy(0, y_offset, shot_width, new_height)
                screenshot = crop_rect
            # Scale screenshot to PNG overlay size
            screenshot = screenshot.scaled(png_width, png_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        # Save the processed screenshot
        csra_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'images', 'csra_overlay.png')
        screenshot.save(csra_path, "PNG")
        self.csra_overlay_path = csra_path
        self.csra_overlay_pixmap = screenshot
        self.load_csra_overlay()
        return True

    def load_csra_overlay(self):
        """Load the CSRA overlay image as a QGraphicsPixmapItem, using the same scaling/position as PNG overlay if available."""
        if not hasattr(self, 'csra_overlay_path') or not os.path.exists(self.csra_overlay_path):
            return
        image = QImage(self.csra_overlay_path)
        if image.isNull():
            return
        pixmap = QPixmap.fromImage(image)
        # Remove existing CSRA overlay item if present
        if hasattr(self, 'csra_overlay_item') and self.csra_overlay_item:
            self.view.scene.removeItem(self.csra_overlay_item)
            self.csra_overlay_item = None
        self.csra_overlay_item = QGraphicsPixmapItem(pixmap)
        # Use same scaling/position as PNG overlay if available
        if hasattr(self, 'geotiff_png_pixmap_item') and self.geotiff_png_pixmap_item:
            scale = self.geotiff_png_pixmap_item.scale()
            pos = self.geotiff_png_pixmap_item.pos()
            self.csra_overlay_item.setScale(scale)
            self.csra_overlay_item.setPos(pos)
        elif hasattr(self, 'geotiff_tiff_pixmap_item') and self.geotiff_tiff_pixmap_item:
            tiff_rect = self.geotiff_tiff_pixmap_item.boundingRect()
            scale_x = tiff_rect.width() / image.width()
            scale_y = tiff_rect.height() / image.height()
            scale = min(scale_x, scale_y)
            self.csra_overlay_item.setScale(scale)
            tiff_pos = self.geotiff_tiff_pixmap_item.pos()
            csra_width = image.width() * scale
            csra_height = image.height() * scale
            x_offset = (tiff_rect.width() - csra_width) / 2
            y_offset = (tiff_rect.height() - csra_height) / 2
            self.csra_overlay_item.setPos(tiff_pos.x() + x_offset, tiff_pos.y() + y_offset)
        self.csra_overlay_item.setZValue(0.5)  # Between GeoTIFF and content
        self.csra_overlay_item.setVisible(False)  # Only show if selected
        self.view.scene.addItem(self.csra_overlay_item)

    def set_overlay_type(self, overlay_type):
        """Switch between PNG and CSRA overlays. Only one is visible at a time. If unavailable, hide overlays. No status bar message."""
        # Hide both overlays first
        if hasattr(self, 'geotiff_png_pixmap_item') and self.geotiff_png_pixmap_item:
            self.geotiff_png_pixmap_item.setVisible(False)
        if hasattr(self, 'csra_overlay_item') and self.csra_overlay_item:
            self.csra_overlay_item.setVisible(False)
        # Show the selected overlay if available
        overlay_shown = False
        if overlay_type == 'MAP_TOPO' and hasattr(self, 'geotiff_png_pixmap_item') and self.geotiff_png_pixmap_item:
            self.geotiff_png_pixmap_item.setVisible(True)
            self.active_overlay_type = 'MAP_TOPO'
            overlay_shown = True
        elif overlay_type == 'CSRA' and hasattr(self, 'csra_overlay_item') and self.csra_overlay_item:
            self.csra_overlay_item.setVisible(True)
            self.active_overlay_type = 'CSRA'
            overlay_shown = True
        else:
            self.active_overlay_type = None
        # Update overlay status label if palette exists
        if hasattr(self, 'palette'):
            self.palette.update_overlay_status()
        # No status bar message here

    def handle_overlay_radio(self, overlay_type, checked):
        if not checked:
            return
        png_loaded = self.png_loaded
        csra_captured = hasattr(self, '_csra_captured') and self._csra_captured
        print(f"[DEBUG] handle_overlay_radio: overlay_type={overlay_type}, checked={checked}, png_loaded={png_loaded}, csra_captured={csra_captured}")
        if overlay_type == 'MAP_TOPO':
            if png_loaded:
                self.main_window.set_overlay_type('MAP_TOPO')
            else:
                QMessageBox.warning(self, "MAP_TOPO Not Available", "No MAP_TOPO overlay is available. Please load a MAP_TOPO overlay first or use CSRA.")
                if csra_captured:
                    self.overlay_csra_radio.setChecked(True)
                else:
                    self.overlay_map_radio.setChecked(False)
        elif overlay_type == 'CSRA':
            if csra_captured:
                self.main_window.set_overlay_type('CSRA')
            else:
                QMessageBox.warning(self, "CSRA Not Available", "No CSRA capture is available. Please capture CSRA first or use MAP_TOPO overlay.")
                if png_loaded:
                    self.overlay_map_radio.setChecked(True)
                else:
                    self.overlay_csra_radio.setChecked(False)
        self.update_overlay_status()

    def toggle_health_bars(self, state):
        show = bool(state)
        for obj in self.objects:
            if hasattr(obj, 'set_health_bar_visible'):
                obj.set_health_bar_visible(show)

    def update_health_bar_panel_visibility(self):
        # Show Health Bar Control only if there are live objects
        if hasattr(self.palette, 'health_bar_group'):
            self.palette.health_bar_group.setVisible(len(self.objects) > 0)

    def on_object_moved(self, obj):
        print(f"[DEBUG][SIM] Object moved: {obj.name}")
        self.update_simulation()

    def on_los_polygon_updated(self, obj):
        print(f"[DEBUG][SIM] LOS polygon updated for: {obj.name}")
        # self.update_simulation()

    def generate_simulation_report(self):
        """Generate a multi-page PDF fight report with advanced charts and offer it for download."""
        if not self.damage_log:
            QMessageBox.information(self, "No Data", "No damage data to report.")
            return
        # --- Parse Ami/Ennemi status ---
        def get_side(name):
            return 'Ami' if 'ennemi' not in name.lower() else 'Ennemi'
        # --- Summarize damage by unit (received and dealt) ---
        damage_received = {}
        damage_dealt = {}
        weapon_type_counts = {}
        timeline = []
        for entry in self.damage_log:
            tgt = entry['target']
            atk = entry['attacker']
            wtype = entry['weapon']
            dmg = entry['damage']
            t = entry.get('time', None)
            # Received
            damage_received.setdefault(tgt, 0)
            damage_received[tgt] += dmg
            # Dealt
            damage_dealt.setdefault(atk, 0)
            damage_dealt[atk] += dmg
            # Weapon type
            weapon_type_counts[wtype] = weapon_type_counts.get(wtype, 0) + dmg
            # Timeline
            timeline.append((t, atk, tgt, wtype, dmg))
        # --- Prepare Ami/Ennemi split ---
        all_units = set(list(damage_received.keys()) + list(damage_dealt.keys()))
        unit_sides = {u: get_side(u) for u in all_units}
        # --- PDF Generation ---
        from matplotlib.backends.backend_pdf import PdfPages
        import matplotlib.pyplot as plt
        import tempfile
        import os
        # Use a temp file for the PDF
        tmp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        with PdfPages(tmp_pdf.name) as pdf:
            # --- 1. Summary Page ---
            fig, ax = plt.subplots(figsize=(8.5, 6))
            ax.axis('off')
            total_damage = sum(damage_received.values())
            ami_units = [u for u in all_units if unit_sides[u] == 'Ami']
            ennemi_units = [u for u in all_units if unit_sides[u] == 'Ennemi']
            survivors_ami = [u for u in ami_units if damage_received.get(u, 0) < 1]
            survivors_ennemi = [u for u in ennemi_units if damage_received.get(u, 0) < 1]
            txt = f"Simulation Fight Report\n\n"
            txt += f"Total Damage: {total_damage}\n"
            txt += f"Ami Units: {len(ami_units)} | Ennemi Units: {len(ennemi_units)}\n"
            txt += f"Ami Survivors: {len(survivors_ami)} | Ennemi Survivors: {len(survivors_ennemi)}\n"
            txt += f"\n---\n"
            txt += f"Top Damage Dealers (Ami):\n"
            for u in sorted(ami_units, key=lambda x: damage_dealt.get(x, 0), reverse=True)[:5]:
                txt += f"  {u}: {damage_dealt.get(u, 0)}\n"
            txt += f"\nTop Damage Dealers (Ennemi):\n"
            for u in sorted(ennemi_units, key=lambda x: damage_dealt.get(x, 0), reverse=True)[:5]:
                txt += f"  {u}: {damage_dealt.get(u, 0)}\n"
            ax.text(0.05, 0.95, txt, va='top', ha='left', fontsize=12, family='monospace')
            pdf.savefig(fig)
            plt.close(fig)
            # --- 2. Bar Chart: Damage Received (Ami/Ennemi split) ---
            fig, ax = plt.subplots(figsize=(10, 5))
            ami_vals = [damage_received[u] for u in ami_units]
            ennemi_vals = [damage_received[u] for u in ennemi_units]
            ax.bar(ami_units, ami_vals, color='#4A90E2', label='Ami')
            ax.bar(ennemi_units, ennemi_vals, color='#E74C3C', label='Ennemi')
            ax.set_title('Damage Received by Unit (Ami/Ennemi)')
            ax.set_xlabel('Unit')
            ax.set_ylabel('Total Damage')
            ax.tick_params(axis='x', rotation=45)
            ax.legend()
            pdf.savefig(fig)
            plt.close(fig)
            # --- 3. Bar Chart: Damage Dealt (Ami/Ennemi split) ---
            fig, ax = plt.subplots(figsize=(10, 5))
            ami_vals = [damage_dealt[u] for u in ami_units]
            ennemi_vals = [damage_dealt[u] for u in ennemi_units]
            ax.bar(ami_units, ami_vals, color='#4A90E2', label='Ami')
            ax.bar(ennemi_units, ennemi_vals, color='#E74C3C', label='Ennemi')
            ax.set_title('Damage Dealt by Unit (Ami/Ennemi)')
            ax.set_xlabel('Unit')
            ax.set_ylabel('Total Damage')
            ax.tick_params(axis='x', rotation=45)
            ax.legend()
            pdf.savefig(fig)
            plt.close(fig)
            # --- 4. Pie Chart: Damage by Weapon Type ---
            fig, ax = plt.subplots(figsize=(6, 6))
            ax.pie(weapon_type_counts.values(), labels=weapon_type_counts.keys(), autopct='%1.1f%%', startangle=140)
            ax.set_title('Damage by Weapon Type')
            pdf.savefig(fig)
            plt.close(fig)
            # --- 5. Timeline Chart: Damage Events ---
            fig, ax = plt.subplots(figsize=(12, 5))
            times = [t[0] for t in timeline]
            dmg_vals = [t[4] for t in timeline]
            colors = ['#4A90E2' if get_side(t[2]) == 'Ami' else '#E74C3C' for t in timeline]
            ax.scatter(times, dmg_vals, c=colors, alpha=0.7)
            ax.set_title('Timeline of Damage Events')
            ax.set_xlabel('Scene Index / Time')
            ax.set_ylabel('Damage')
            pdf.savefig(fig)
            plt.close(fig)
        # --- Offer file dialog to save/download PDF ---
        save_path, _ = QFileDialog.getSaveFileName(self, "Save Simulation Report", "fight_report.pdf", "PDF Files (*.pdf)")
        if save_path:
            import shutil
            shutil.copy(tmp_pdf.name, save_path)
            QMessageBox.information(self, "Report Saved", f"Simulation report saved to:\n{save_path}")
        else:
            QMessageBox.information(self, "Report Not Saved", "Simulation report was not saved.")
        try:
            os.unlink(tmp_pdf.name)
        except PermissionError as e:
            print(f"[WARNING] Could not delete temp PDF: {e}")
        except Exception as e:
            print(f"[WARNING] Unexpected error deleting temp PDF: {e}")

    def play_full_sequence(self):
        if self.sequence_is_playing or len(self.scenes) < 2:
            return
        self.sequence_is_playing = True
        self.current_scene_index = 0
        self.animate_to_next_scene()

    def on_speed_slider_changed(self, value):
        speed_map = {0: 1, 1: 2, 2: 4, 3: 6, 4: 8, 5: 10, 6: 20}
        self.speed_multiplier = speed_map.get(value, 1)
        self.palette.speed_label.setText(f"Current Speed: x{self.speed_multiplier}")
        # --- Live update of animation speed ---
        if hasattr(self, 'current_animation_group') and self.current_animation_group and self.current_animation_group.state() == self.current_animation_group.Running:
            if hasattr(self, '_animation_meta'):
                # Stop the current animation group
                self.current_animation_group.stop()
                new_animation_group = QParallelAnimationGroup(self)
                new_meta = []
                for meta in self._animation_meta:
                    obj = meta['obj']
                    end_pos = meta['end_pos']
                    base_duration = meta['base_duration']
                    # Get current position
                    current_pos = obj.graphic.pos()
                    # Compute remaining distance
                    main_win = self
                    if hasattr(main_win, 'get_xyz_from_scene_point') and hasattr(main_win, 'haversine'):
                        xyz1 = main_win.get_xyz_from_scene_point(current_pos)
                        xyz2 = main_win.get_xyz_from_scene_point(end_pos)
                        if xyz1 is not None and xyz2 is not None:
                            lon1, lat1, _ = xyz1
                            lon2, lat2, _ = xyz2
                            remaining_distance = main_win.haversine(lon1, lat1, lon2, lat2)
                        else:
                            dx = end_pos.x() - current_pos.x()
                            dy = end_pos.y() - current_pos.y()
                            remaining_distance = (dx ** 2 + dy ** 2) ** 0.5
                    else:
                        dx = end_pos.x() - current_pos.x()
                        dy = end_pos.y() - current_pos.y()
                        remaining_distance = (dx ** 2 + dy ** 2) ** 0.5
                    # If already at target, skip
                    if abs(current_pos.x() - end_pos.x()) < 1e-2 and abs(current_pos.y() - end_pos.y()) < 1e-2:
                        continue
                    # Compute new duration for remaining distance
                    total_distance = meta.get('total_distance', None)
                    if total_distance is None:
                        # Compute from start_pos to end_pos
                        start_pos = meta['start_pos']
                        if hasattr(main_win, 'get_xyz_from_scene_point') and hasattr(main_win, 'haversine'):
                            xyz1 = main_win.get_xyz_from_scene_point(start_pos)
                            xyz2 = main_win.get_xyz_from_scene_point(end_pos)
                            if xyz1 is not None and xyz2 is not None:
                                lon1, lat1, _ = xyz1
                                lon2, lat2, _ = xyz2
                                total_distance = main_win.haversine(lon1, lat1, lon2, lat2)
                            else:
                                dx = end_pos.x() - start_pos.x()
                                dy = end_pos.y() - start_pos.y()
                                total_distance = (dx ** 2 + dy ** 2) ** 0.5
                        else:
                            dx = end_pos.x() - start_pos.x()
                            dy = end_pos.y() - start_pos.y()
                            total_distance = (dx ** 2 + dy ** 2) ** 0.5
                    # Proportion of remaining distance
                    if total_distance > 0:
                        remaining_ratio = remaining_distance / total_distance
                    else:
                        remaining_ratio = 0
                    new_total_duration = int(base_duration / self.speed_multiplier) if self.speed_multiplier > 0 else base_duration
                    new_duration = int(new_total_duration * remaining_ratio)
                    if new_duration < 1:
                        new_duration = 1
                    wrapper = GraphicsItemAnimationWrapper(obj.graphic)
                    animation = QPropertyAnimation(wrapper, b"pos")
                    animation.setDuration(new_duration)
                    animation.setStartValue(current_pos)
                    animation.setEndValue(end_pos)
                    new_animation_group.addAnimation(animation)
                    new_meta.append({
                        'animation': animation,
                        'base_duration': base_duration,
                        'start_pos': current_pos,
                        'end_pos': end_pos,
                        'obj': obj,
                        'total_distance': remaining_distance
                    })
                # Replace the animation group and meta
                self.current_animation_group = new_animation_group
                self._animation_meta = new_meta
                if self.current_animation_group.receivers(self.current_animation_group.finished) > 0:
                    self.current_animation_group.finished.disconnect()
                self.current_animation_group.finished.connect(lambda: self.on_animation_finished(self.current_scene_index))
                self.current_animation_group.start()

class ObjectInfoWidget(QWidget):
    def __init__(self, obj, parent=None):
        super().__init__(parent)
        self.obj = obj
        self.setup_ui()
        
    def setup_ui(self):
        # Main layout with even tighter spacing
        layout = QVBoxLayout(self)
        layout.setSpacing(1)
        layout.setContentsMargins(1, 1, 1, 1)
        
        # Style sheets
        label_style = """
            QLabel {
                color: #2c3e50;
                font-size: 8pt;
                font-weight: bold;
                padding: 0px;
                background: transparent;
            }
        """
        
        input_style = """
            QLineEdit {
                font-size: 8pt;
                padding: 2px 4px;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                background: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background: #f8f9fa;
            }
            QLineEdit:hover {
                background: #f5f6f7;
            }
            QLineEdit[modified="true"] {
                border-left: 2px solid #2ecc71;
            }
        """
        
        # Create form rows with enhanced styling
        self.create_form_row(layout, "Name:", obj.name, "name", label_style, input_style)
        self.create_form_row(layout, "Designation:", obj.name, "designation", label_style, input_style)
        self.create_form_row(layout, "Matériel:", obj.object_type, "materiel", label_style, input_style)
        self.create_form_row(layout, "Parent:", getattr(obj, 'parent', ''), "parent", label_style, input_style)
        
        # Add save/reset buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(2)
        
        self.save_button = QPushButton("Save")
        self.reset_button = QPushButton("Reset")
        
        # Style buttons
        button_style = """
            QPushButton {
                font-size: 8pt;
                padding: 2px 8px;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                background: #f8f9fa;
                min-height: 20px;
                max-height: 20px;
            }
            QPushButton:hover {
                background: #e9ecef;
                border-color: #3498db;
            }
            QPushButton:pressed {
                background: #dee2e6;
            }
        """
        
        self.save_button.setStyleSheet(button_style)
        self.reset_button.setStyleSheet(button_style)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.reset_button)
        layout.addLayout(button_layout)
        
        # Connect buttons
        self.save_button.clicked.connect(self.save_changes)
        self.reset_button.clicked.connect(self.reset_changes)
        
        # Store initial values for reset
        self.initial_values = {
            'name': obj.name,
            'designation': obj.name,
            'materiel': obj.object_type,
            'parent': getattr(obj, 'parent', '')
        }
        
        # Set size constraints
        self.setMaximumHeight(140)
        
    def create_form_row(self, parent_layout, label_text, initial_value, field_name, label_style, input_style):
        row = QHBoxLayout()
        row.setSpacing(2)
        
        # Label with fixed width and styling
        label = QLabel(label_text)
        label.setFixedWidth(65)
        label.setStyleSheet(label_style)
        
        # Input field with styling and features
        edit = QLineEdit(initial_value)
        edit.setStyleSheet(input_style)
        edit.setFixedHeight(20)
        edit.setProperty("modified", False)
        
        # Store reference
        setattr(self, f"{field_name}_edit", edit)
        
        # Add tooltip
        edit.setToolTip(f"Enter {field_name}")
        
        # Connect signals
        edit.textChanged.connect(lambda: self.handle_text_changed(edit, field_name))
        edit.editingFinished.connect(lambda: self.handle_editing_finished(edit))
        
        row.addWidget(label)
        row.addWidget(edit)
        parent_layout.addLayout(row)
        
    def handle_text_changed(self, edit, field_name):
        # Visual feedback for modified fields
        is_modified = edit.text() != self.initial_values[field_name]
        edit.setProperty("modified", is_modified)
        edit.style().unpolish(edit)
        edit.style().polish(edit)
        
        # Enable/disable save button based on any modifications
        any_modified = any(
            getattr(self, f"{field}_edit").text() != self.initial_values[field]
            for field in ['name', 'designation', 'materiel', 'parent']
        )
        self.save_button.setEnabled(any_modified)
        self.reset_button.setEnabled(any_modified)
        
    def handle_editing_finished(self, edit):
        # Optional: Add validation here
        pass
        
    def save_changes(self):
        # Update object with new values
        self.obj.name = self.name_edit.text()
        self.obj.object_type = self.materiel_edit.text()
        self.obj.parent = self.parent_edit.text()
        
        # Update initial values
        self.initial_values = {
            'name': self.name_edit.text(),
            'designation': self.designation_edit.text(),
            'materiel': self.materiel_edit.text(),
            'parent': self.parent_edit.text()
        }
        
        # Reset modified state
        for field in ['name', 'designation', 'materiel', 'parent']:
            edit = getattr(self, f"{field}_edit")
            edit.setProperty("modified", False)
            edit.style().unpolish(edit)
            edit.style().polish(edit)
        
        # Disable buttons
        self.save_button.setEnabled(False)
        self.reset_button.setEnabled(False)
        
        # Find and update tree widget item
        tree = self.parent().parent()
        if isinstance(tree, QTreeWidget):
            root = tree.invisibleRootItem()
            for i in range(root.childCount()):
                item = root.child(i)
                if item.data(0, Qt.UserRole) == self.obj:
                    item.setText(0, self.obj.name)
                    break
        
        # Update info labels on map
        main_window = self.main_window
        if hasattr(main_window, 'remove_all_info_labels') and hasattr(main_window, 'show_info_labels'):
            main_window.remove_all_info_labels()
            main_window.show_info_labels(self.obj)
            
    def reset_changes(self):
        # Reset all fields to initial values
        for field, value in self.initial_values.items():
            edit = getattr(self, f"{field}_edit")
            edit.setText(value)
            edit.setProperty("modified", False)
            edit.style().unpolish(edit)
            edit.style().polish(edit)
        
        # Disable buttons
        self.save_button.setEnabled(False)
        self.reset_button.setEnabled(False)

# Add this class after the imports and before MainWindow
class GraphicsItemAnimationWrapper(QObject):
    def __init__(self, graphics_item):
        super().__init__()
        self._graphics_item = graphics_item
    @pyqtProperty(QPointF)
    def pos(self):
        return self._graphics_item.pos()
    @pos.setter
    def pos(self, value):
        self._graphics_item.setPos(value)


class TimePreviewDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Time Preview")
        self.setModal(True)
        self.resize(1200, 800)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create view
        self.view = QGraphicsView()
        self.scene = QGraphicsScene()
        self.view.setScene(self.scene)
        self.view.setRenderHint(QPainter.Antialiasing)
        self.view.setRenderHint(QPainter.SmoothPixmapTransform)
        self.view.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.view.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        layout.addWidget(self.view)
        
        # Add close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(close_btn)

    def show_global_preview(self, objects, main_scene, scenes):
        """Show first scene of each time point (T1, T2, T3) in a single view"""
        if not scenes:
            return

        self.scene.clear()
        
        # Get reference to main view
        main_view = self.parent().view
        
        # Create a snapshot of the current view
        pixmap = QPixmap(main_view.viewport().size())
        pixmap.fill(Qt.white)
        painter = QPainter(pixmap)
        main_view.render(painter)
        painter.end()
        
        # Add the snapshot to our scene
        background = QGraphicsPixmapItem(pixmap)
        background.setZValue(-1)
        self.scene.addItem(background)
        
        # Set scene rect to match the pixmap
        self.scene.setSceneRect(background.boundingRect())
        
        # Find first scene for each time point
        time_scenes = {"T1": None, "T2": None, "T3": None}
        for scene in scenes:
            time = scene['time']
            if time in time_scenes and time_scenes[time] is None:
                time_scenes[time] = scene
        
        # Add objects at their positions
        for obj in objects:
            for time, scene in time_scenes.items():
                if scene and obj.name in scene['objects']:
                    pos_data = scene['objects'][obj.name]
                    
                    if hasattr(obj, 'graphic') and isinstance(obj.graphic, QGraphicsPixmapItem):
                        base_image_name = obj.object_type.split('_T')[0] if '_T' in obj.object_type else obj.object_type
                        image_path = os.path.join(
                            os.path.dirname(__file__),
                            '..',
                            'assets',
                            'images',
                            f"{base_image_name}_{time}.png"
                        )
                        
                        try:
                            if os.path.exists(image_path):
                                pixmap = QPixmap(image_path)
                            else:
                                default_path = os.path.join(
                                    os.path.dirname(__file__),
                                    '..',
                                    'assets',
                                    'images',
                                    f"{base_image_name}.png"
                                )
                                pixmap = QPixmap(default_path)
                            
                            if not pixmap.isNull():
                                pixmap = pixmap.scaled(obj.icon_size, obj.icon_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                                new_item = QGraphicsPixmapItem(pixmap)
                                
                                # Convert scene coordinates to view coordinates
                                view_pos = main_view.mapFromScene(pos_data['x'], pos_data['y'])
                                scene_pos = self.view.mapToScene(view_pos)
                                new_item.setPos(scene_pos.x() - 20, scene_pos.y() - 20)
                                new_item.setRotation(pos_data.get('rotation', 0))
                                new_item.setZValue(1)
                                self.scene.addItem(new_item)
                        except Exception as e:
                            print(f"Error creating preview for {obj.name} at {time}: {str(e)}")
        
        # Fit view to scene
        self.view.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)

    def showEvent(self, event):
        """Handle show event to ensure proper view fitting"""
        super().showEvent(event)
        if self.scene.items():
            self.view.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)

    def resizeEvent(self, event):
        """Handle window resize to maintain view"""
        super().resizeEvent(event)
        if self.scene.items():
            self.view.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)

# --- Custom Switch Widget ---
class Switch(QAbstractButton):
    def __init__(self, label_on="ON", label_off="OFF", parent=None):
        super().__init__(parent)
        self.setCheckable(True)
        self.setChecked(False)  # OFF by default
        self.label_on = label_on
        self.label_off = label_off
        self.setFixedSize(65, 20)  # New size
        self.setCursor(Qt.PointingHandCursor)  # Show hand cursor for clarity
        print(f"[DEBUG] Switch constructed: {self.label_on}/{self.label_off}, enabled={self.isEnabled()}, checked={self.isChecked()}")

    def sizeHint(self):
        return QSize(65, 20)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        rect = self.rect()
        # Background
        if self.isChecked():
            painter.setBrush(QColor(76, 192, 76))  # Green #4CC04C
            painter.setPen(Qt.black)
        else:
            painter.setBrush(QColor(255, 160, 0))  # Orange #FFA000
            painter.setPen(Qt.black)
        painter.drawRoundedRect(rect, 10, 10)
        # Handle
        handle_rect = QRectF(rect.left() + (rect.width() - 20 if self.isChecked() else 0), rect.top(), 20, 20)
        painter.setBrush(Qt.black)
        painter.drawEllipse(handle_rect)
        # Text
        painter.setPen(Qt.black)
        painter.setFont(QFont("Arial", 9, QFont.Bold))
        text = self.label_on if self.isChecked() else self.label_off
        painter.drawText(rect, Qt.AlignCenter, text)

    def mouseReleaseEvent(self, event):
        # Toggle on any left mouse release, anywhere on the switch (standard UX)
        if event.button() == Qt.LeftButton:
            self.setChecked(not self.isChecked())
            self.toggled.emit(self.isChecked())
            print(f"[DEBUG] Switch toggled to {self.isChecked()} by click")
            event.accept()  # Explicitly accept the event
        else:
            super().mouseReleaseEvent(event)
