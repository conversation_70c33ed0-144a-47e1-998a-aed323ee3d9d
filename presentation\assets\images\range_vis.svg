<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <!-- Range Visualization -->
  <defs>
    <radialGradient id="ap-gradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:rgb(225,119,38);stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:rgb(225,119,38);stop-opacity:0"/>
    </radialGradient>
    <radialGradient id="ac-gradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:rgb(151,208,56);stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:rgb(151,208,56);stop-opacity:0"/>
    </radialGradient>
    <radialGradient id="missile-gradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:rgb(0,91,91);stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:rgb(0,91,91);stop-opacity:0"/>
    </radialGradient>
  </defs>
  
  <!-- Range Circles -->
  <g class="ranges">
    <!-- Missile Range -->
    <circle cx="400" cy="200" r="150" fill="url(#missile-gradient)">
      <animate attributeName="r" values="150;155;150" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- AC Range -->
    <circle cx="400" cy="200" r="100" fill="url(#ac-gradient)">
      <animate attributeName="r" values="100;105;100" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- AP Range -->
    <circle cx="400" cy="200" r="50" fill="url(#ap-gradient)">
      <animate attributeName="r" values="50;55;50" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Unit Icon -->
  <circle cx="400" cy="200" r="15" fill="#4a90e2" stroke="white" stroke-width="2"/>
  
  <!-- Labels -->
  <g class="labels">
    <text x="400" y="100" text-anchor="middle" fill="white">AP Range: 220m</text>
    <text x="400" y="80" text-anchor="middle" fill="white">AC Range: 420m</text>
    <text x="400" y="60" text-anchor="middle" fill="white">Missile Range: 820m</text>
  </g>
</svg>
