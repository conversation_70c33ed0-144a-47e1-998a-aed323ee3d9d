(['C:\\Users\\<USER>\\Desktop\\manoeuvres simulation\\main.py'],
 ['C:\\Users\\<USER>\\Desktop\\manoeuvres simulation'],
 [],
 [('C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pygame\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('manoeuvres_simulation\\assets\\images\\Bataillon_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto-Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto-Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\S_gpt_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\S_gpt_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\S_gpt_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\S_gpt_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\csra_overlay.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\csra_overlay.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_brigade.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_brigade.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_company.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_company.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_section.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_section.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_mecaff.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_mecaff.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_mecaqq.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_mecaqq.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_motoss.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_motoss.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ami33.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ami33.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ennemi33.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ennemi33.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\logo_sm.ico',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.ico',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\logo_sm.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\m113_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\m113_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\m113_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\m113_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_amiW.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_amiW.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ennemiW.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ennemiW.png',
   'DATA')],
 '3.11.1 (tags/v3.11.1:a7a450f, Dec  6 2022, 19:58:39) [MSC v.1934 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_osgeo',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_osgeo.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres simulation\\main.py',
   'PYSOURCE')],
 [('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\signal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shutil.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\inspect.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\token.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bisect.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\quopri.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pathlib.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\struct.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gettext.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('manoeuvres_simulation.utils',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\utils.py',
   'PYMODULE'),
  ('manoeuvres_simulation',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\__init__.py',
   'PYMODULE'),
  ('manoeuvres_simulation.gui',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\gui.py',
   'PYMODULE'),
  ('manoeuvres_simulation.project_setup_dialog',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\project_setup_dialog.py',
   'PYMODULE'),
  ('manoeuvres_simulation.animation',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\animation.py',
   'PYMODULE'),
  ('manoeuvres_simulation.objects',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\objects.py',
   'PYMODULE'),
  ('manoeuvres_simulation.physics',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\physics.py',
   'PYMODULE'),
  ('pymunk',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\__init__.py',
   'PYMODULE'),
  ('pymunk.vec2d',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\vec2d.py',
   'PYMODULE'),
  ('pymunk.transform',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\transform.py',
   'PYMODULE'),
  ('pymunk.space_debug_draw_options',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\space_debug_draw_options.py',
   'PYMODULE'),
  ('pymunk.space',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\space.py',
   'PYMODULE'),
  ('pymunk._pickle',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_pickle.py',
   'PYMODULE'),
  ('pymunk._callbacks',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_callbacks.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\platform.py',
   'PYMODULE'),
  ('pymunk.shapes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\shapes.py',
   'PYMODULE'),
  ('pymunk._typing_attr',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_typing_attr.py',
   'PYMODULE'),
  ('pymunk.shape_filter',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\shape_filter.py',
   'PYMODULE'),
  ('pymunk.query_info',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\query_info.py',
   'PYMODULE'),
  ('pymunk.contact_point_set',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\contact_point_set.py',
   'PYMODULE'),
  ('pymunk.constraints',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\constraints.py',
   'PYMODULE'),
  ('pymunk.collision_handler',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\collision_handler.py',
   'PYMODULE'),
  ('pymunk.body',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\body.py',
   'PYMODULE'),
  ('pymunk.bb',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\bb.py',
   'PYMODULE'),
  ('pymunk.arbiter',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\arbiter.py',
   'PYMODULE'),
  ('pymunk._version',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_version.py',
   'PYMODULE'),
  ('pymunk._chipmunk_cffi',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_chipmunk_cffi.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('manoeuvres_simulation.draw',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\draw.py',
   'PYMODULE'),
  ('osgeo.gdal',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\gdal.py',
   'PYMODULE'),
  ('osgeo.gdal_array',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\gdal_array.py',
   'PYMODULE'),
  ('osgeo.osr',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\osr.py',
   'PYMODULE'),
  ('osgeo.ogr',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\ogr.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('osgeo.gdalconst',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\gdalconst.py',
   'PYMODULE'),
  ('osgeo',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\doctest.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tty.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\glob.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cmd.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('pyqt5_fugueicons',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pyqt5_fugueicons\\__init__.py',
   'PYMODULE'),
  ('pyqt5_fugueicons.resources',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pyqt5_fugueicons\\resources.py',
   'PYMODULE'),
  ('pyqt5_fugueicons.movie',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pyqt5_fugueicons\\movie.py',
   'PYMODULE'),
  ('pyqt5_fugueicons.icon',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pyqt5_fugueicons\\icon.py',
   'PYMODULE')],
 [('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pymunk\\_chipmunk.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_chipmunk.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_gdal_array.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_gdal_array.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_osr.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_osr.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_ogr.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_ogr.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_gdalconst.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_gdalconst.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_gdal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_gdal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('osgeo\\gdal304.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\gdal304.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('osgeo\\geos_c.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\geos_c.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('manoeuvres_simulation\\assets\\images\\Bataillon_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto-Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto-Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\S_gpt_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\S_gpt_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\S_gpt_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\S_gpt_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\csra_overlay.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\csra_overlay.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_brigade.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_brigade.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_company.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_company.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_section.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_section.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_mecaff.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_mecaff.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_mecaqq.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_mecaqq.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_motoss.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_motoss.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ami33.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ami33.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ennemi33.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ennemi33.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\logo_sm.ico',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.ico',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\logo_sm.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\m113_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\m113_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\m113_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\m113_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_amiW.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_amiW.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ennemiW.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ennemiW.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_GCP.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_GCP.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdSgmtA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdSgmtA.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\gt_ellips.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gt_ellips.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\epsg.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\epsg.wkt',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WStrL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WStrL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_AdmPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_AdmPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdEdg.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdEdg.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_LINZAntarticaMapTileGrid.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_LINZAntarticaMapTileGrid.json',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RvrMgtBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RvrMgtBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\gmlasconf.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gmlasconf.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\vicar.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\vicar.json',
   'DATA'),
  ('osgeo\\data\\proj\\ITRF2000',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\ITRF2000',
   'DATA'),
  ('osgeo\\data\\proj\\CH',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\CH',
   'DATA'),
  ('osgeo\\data\\gdal\\pci_ellips.txt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pci_ellips.txt',
   'DATA'),
  ('osgeo\\data\\gdal\\gdalvrt.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gdalvrt.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_LeveeEdge.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_LeveeEdge.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\s57agencies.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57agencies.csv',
   'DATA'),
  ('osgeo\\data\\proj\\nad.lst',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\nad.lst',
   'DATA'),
  ('osgeo\\data\\gdal\\default.rsc',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\default.rsc',
   'DATA'),
  ('osgeo\\data\\gdal\\s57attributes.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57attributes.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_NZTM2000.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_NZTM2000.json',
   'DATA'),
  ('osgeo\\data\\gdal\\nitf_spec.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\nitf_spec.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_Cstline.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_Cstline.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdASL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdASL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\bag_template.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\bag_template.xml',
   'DATA'),
  ('osgeo\\data\\gdal\\ecw_cs.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ecw_cs.wkt',
   'DATA'),
  ('osgeo\\data\\gdal\\s57expectedinput.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57expectedinput.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\gdalicon.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gdalicon.png',
   'DATA'),
  ('osgeo\\data\\gdal\\seed_2d.dgn',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\seed_2d.dgn',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_MapML_CBMTILE.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_MapML_CBMTILE.json',
   'DATA'),
  ('osgeo\\data\\proj\\nad83',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\nad83',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RailCL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RailCL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\cubewerx_extra.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\cubewerx_extra.wkt',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_v1.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_st_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_st_v1.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\GDALLogoColor.svg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\GDALLogoColor.svg',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_st_uvoh_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_st_uvoh_v1.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\gdalmdiminfo_output.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gdalmdiminfo_output.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\gmlasconf.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gmlasconf.xml',
   'DATA'),
  ('osgeo\\data\\proj\\ITRF2008',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\ITRF2008',
   'DATA'),
  ('osgeo\\data\\gdal\\stateplane.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\stateplane.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_Cntr.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_Cntr.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_SBBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_SBBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\trailer.dxf',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\trailer.dxf',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_CommBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_CommBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\pci_datum.txt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pci_datum.txt',
   'DATA'),
  ('osgeo\\data\\gdal\\nitf_spec.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\nitf_spec.xml',
   'DATA'),
  ('osgeo\\data\\proj\\GL27',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\GL27',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_AdmArea.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_AdmArea.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\other.extra',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\other.extra',
   'DATA'),
  ('osgeo\\data\\proj\\deformation_model.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\deformation_model.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\gml_registry.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gml_registry.xml',
   'DATA'),
  ('osgeo\\data\\gdal\\plscenesconf.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\plscenesconf.json',
   'DATA'),
  ('osgeo\\data\\gdal\\esri_StatePlane_extra.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\esri_StatePlane_extra.wkt',
   'DATA'),
  ('osgeo\\data\\proj\\world',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\world',
   'DATA'),
  ('osgeo\\data\\gdal\\vdv452.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\vdv452.xml',
   'DATA'),
  ('osgeo\\data\\proj\\triangulation.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\triangulation.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdArea.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdArea.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_SBArea.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_SBArea.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_CommPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_CommPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\eedaconf.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\eedaconf.json',
   'DATA'),
  ('osgeo\\data\\gdal\\GDALLogoBW.svg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\GDALLogoBW.svg',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_CadastralParcel.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_CadastralParcel.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\projjson.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\projjson.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\ozi_ellips.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ozi_ellips.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\osmconf.ini',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\osmconf.ini',
   'DATA'),
  ('osgeo\\data\\gdal\\netcdf_config.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\netcdf_config.xsd',
   'DATA'),
  ('osgeo\\data\\proj\\proj.db',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\proj.db',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_CadastralBoundary.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_CadastralBoundary.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ozi_datum.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ozi_datum.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\header.dxf',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\header.dxf',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_CadastralZoning.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_CadastralZoning.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\template_tiles.mapml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\template_tiles.mapml',
   'DATA'),
  ('osgeo\\data\\gdal\\gt_datum.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gt_datum.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_BldL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_BldL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\vdv452.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\vdv452.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_BasicPropertyUnit.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_BasicPropertyUnit.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\seed_3d.dgn',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\seed_3d.dgn',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_BldA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_BldA.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_ElevPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_ElevPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_ob_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_ob_v1.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\ITRF2014',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\ITRF2014',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WA.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\proj.ini',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\proj.ini',
   'DATA'),
  ('osgeo\\data\\gdal\\GDALLogoGS.svg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\GDALLogoGS.svg',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdMgtBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdMgtBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\pdfcomposition.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pdfcomposition.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\s57objectclasses.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57objectclasses.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_SBAPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_SBAPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ogrvrt.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ogrvrt.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_AdmBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_AdmBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_MapML_APSTILE.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_MapML_APSTILE.json',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WStrA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WStrA.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\pds4_template.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pds4_template.xml',
   'DATA'),
  ('osgeo\\data\\proj\\nad27',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\nad27',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdCompt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdCompt.gfs',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\base_library.zip',
   'DATA')])
