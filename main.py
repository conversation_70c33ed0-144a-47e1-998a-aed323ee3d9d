import sys
from PyQt5.QtWidgets import QApplication
from manoeuvres_simulation.gui import MainWindow
from manoeuvres_simulation.utils import resource_path

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    # Set icon using resource_path
    from PyQt5.QtGui import QIcon
    icon_path = resource_path('assets/images/logo_sm.ico')
    import os
    if os.path.exists(icon_path):
        window.setWindowIcon(QIcon(icon_path))
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 