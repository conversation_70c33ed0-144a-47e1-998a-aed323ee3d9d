<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <!-- Evolution Timeline -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1a2a6c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b21f1f;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Timeline Base -->
  <line x1="50" y1="200" x2="750" y2="200" stroke="url(#grad1)" stroke-width="4"/>
  
  <!-- Version Markers -->
  <g class="version-markers">
    <circle cx="100" cy="200" r="10" fill="#4a90e2">
      <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="100" y="240" text-anchor="middle" fill="white">v1.0</text>
    
    <circle cx="250" cy="200" r="10" fill="#50e3c2">
      <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="250" y="240" text-anchor="middle" fill="white">v2.0</text>
    
    <circle cx="400" cy="200" r="10" fill="#f5a623">
      <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="400" y="240" text-anchor="middle" fill="white">v3.0</text>
    
    <circle cx="550" cy="200" r="10" fill="#b8e986">
      <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="550" y="240" text-anchor="middle" fill="white">v4.0</text>
    
    <circle cx="700" cy="200" r="10" fill="#e74c3c">
      <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="700" y="240" text-anchor="middle" fill="white">v5.0</text>
  </g>
  
  <!-- Feature Labels -->
  <g class="feature-labels">
    <text x="100" y="160" text-anchor="middle" fill="white">Base System</text>
    <text x="250" y="160" text-anchor="middle" fill="white">Animations</text>
    <text x="400" y="160" text-anchor="middle" fill="white">LOS System</text>
    <text x="550" y="160" text-anchor="middle" fill="white">Advanced Features</text>
    <text x="700" y="160" text-anchor="middle" fill="white">Optimization</text>
  </g>
</svg>
