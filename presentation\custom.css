/* Custom styles for the presentation */
.reveal section img {
    border: none;
    box-shadow: none;
    background: transparent;
}

.reveal pre {
    box-shadow: none;
    margin: 20px auto;
    width: 90%;
}

.reveal pre code {
    padding: 20px;
    font-size: 1.2em;
}

.reveal h1 {
    font-size: 2.5em;
    color: #4a90e2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.reveal h2 {
    color: #50e3c2;
}

.reveal h3 {
    color: #f5a623;
}

.mermaid {
    width: 100%;
    height: auto;
}

/* Animated elements */
.reveal .slides section .fragment.fade-up {
    opacity: 0;
    transform: translate(0, 20px);
}

.reveal .slides section .fragment.fade-up.visible {
    opacity: 1;
    transform: translate(0, 0);
}

/* Timeline animation */
.timeline-marker {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Range visualization */
.range-circle {
    animation: expand 3s infinite;
}

@keyframes expand {
    0% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.1); opacity: 0.6; }
    100% { transform: scale(1); opacity: 0.8; }
}
