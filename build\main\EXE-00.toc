('C:\\Users\\<USER>\\Desktop\\manoeuvres simulation\\dist\\main.exe',
 <PERSON>als<PERSON>,
 <PERSON><PERSON>e,
 False,
 ['C:\\Users\\<USER>\\Desktop\\manoeuvres '
  'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.ico'],
 None,
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Desktop\\manoeuvres simulation\\build\\main\\main.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres simulation\\build\\main\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_osgeo',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_osgeo.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres simulation\\main.py',
   'PYSOURCE'),
  ('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pymunk\\_chipmunk.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pymunk\\_chipmunk.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_gdal_array.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_gdal_array.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_osr.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_osr.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_ogr.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_ogr.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_gdalconst.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_gdalconst.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('osgeo\\_gdal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\_gdal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('osgeo\\gdal304.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\gdal304.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('osgeo\\geos_c.dll',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\geos_c.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Bataillon_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Bataillon_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Brigade_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Brigade_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cas_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cas_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Cml_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Cml_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Compagnie_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Compagnie_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Esc_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Esc_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Geb_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Geb_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto-Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto-Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Groupement_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Groupement_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Reco_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Reco_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\S_gpt_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\S_gpt_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\S_gpt_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\S_gpt_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sac_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sac_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Section_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Section_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_Ennemi_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\Sous_groupement_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\csra_overlay.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\csra_overlay.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_brigade.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_brigade.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_company.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_company.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\enmi_section.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\enmi_section.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_meca_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_meca_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_mecaff.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_mecaff.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_mecaqq.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_mecaqq.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T1.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T1.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T2.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T2.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_moto_T3.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_moto_T3.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\gpt_motoss.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\gpt_motoss.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ami33.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ami33.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\jeep_ennemi33.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\jeep_ennemi33.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\logo_sm.ico',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.ico',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\logo_sm.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\logo_sm.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\m113_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\m113_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\m113_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\m113_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ami.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ami.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_amiW.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_amiW.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ennemi.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ennemi.png',
   'DATA'),
  ('manoeuvres_simulation\\assets\\images\\tank_ennemiW.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\manoeuvres_simulation\\assets\\images\\tank_ennemiW.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_GCP.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_GCP.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdSgmtA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdSgmtA.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\gt_ellips.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gt_ellips.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\epsg.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\epsg.wkt',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WStrL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WStrL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_AdmPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_AdmPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdEdg.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdEdg.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_LINZAntarticaMapTileGrid.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_LINZAntarticaMapTileGrid.json',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RvrMgtBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RvrMgtBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\gmlasconf.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gmlasconf.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\vicar.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\vicar.json',
   'DATA'),
  ('osgeo\\data\\proj\\ITRF2000',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\ITRF2000',
   'DATA'),
  ('osgeo\\data\\proj\\CH',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\CH',
   'DATA'),
  ('osgeo\\data\\gdal\\pci_ellips.txt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pci_ellips.txt',
   'DATA'),
  ('osgeo\\data\\gdal\\gdalvrt.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gdalvrt.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_LeveeEdge.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_LeveeEdge.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\s57agencies.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57agencies.csv',
   'DATA'),
  ('osgeo\\data\\proj\\nad.lst',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\nad.lst',
   'DATA'),
  ('osgeo\\data\\gdal\\default.rsc',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\default.rsc',
   'DATA'),
  ('osgeo\\data\\gdal\\s57attributes.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57attributes.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_NZTM2000.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_NZTM2000.json',
   'DATA'),
  ('osgeo\\data\\gdal\\nitf_spec.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\nitf_spec.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_Cstline.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_Cstline.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdASL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdASL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\bag_template.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\bag_template.xml',
   'DATA'),
  ('osgeo\\data\\gdal\\ecw_cs.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ecw_cs.wkt',
   'DATA'),
  ('osgeo\\data\\gdal\\s57expectedinput.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57expectedinput.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\gdalicon.png',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gdalicon.png',
   'DATA'),
  ('osgeo\\data\\gdal\\seed_2d.dgn',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\seed_2d.dgn',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_MapML_CBMTILE.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_MapML_CBMTILE.json',
   'DATA'),
  ('osgeo\\data\\proj\\nad83',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\nad83',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RailCL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RailCL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\cubewerx_extra.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\cubewerx_extra.wkt',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_v1.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_st_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_st_v1.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\GDALLogoColor.svg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\GDALLogoColor.svg',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_st_uvoh_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_st_uvoh_v1.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\gdalmdiminfo_output.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gdalmdiminfo_output.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\gmlasconf.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gmlasconf.xml',
   'DATA'),
  ('osgeo\\data\\proj\\ITRF2008',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\ITRF2008',
   'DATA'),
  ('osgeo\\data\\gdal\\stateplane.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\stateplane.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_Cntr.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_Cntr.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_SBBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_SBBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\trailer.dxf',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\trailer.dxf',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_CommBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_CommBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\pci_datum.txt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pci_datum.txt',
   'DATA'),
  ('osgeo\\data\\gdal\\nitf_spec.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\nitf_spec.xml',
   'DATA'),
  ('osgeo\\data\\proj\\GL27',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\GL27',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_AdmArea.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_AdmArea.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\other.extra',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\other.extra',
   'DATA'),
  ('osgeo\\data\\proj\\deformation_model.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\deformation_model.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\gml_registry.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gml_registry.xml',
   'DATA'),
  ('osgeo\\data\\gdal\\plscenesconf.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\plscenesconf.json',
   'DATA'),
  ('osgeo\\data\\gdal\\esri_StatePlane_extra.wkt',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\esri_StatePlane_extra.wkt',
   'DATA'),
  ('osgeo\\data\\proj\\world',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\world',
   'DATA'),
  ('osgeo\\data\\gdal\\vdv452.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\vdv452.xml',
   'DATA'),
  ('osgeo\\data\\proj\\triangulation.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\triangulation.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdArea.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdArea.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_SBArea.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_SBArea.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_CommPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_CommPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\eedaconf.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\eedaconf.json',
   'DATA'),
  ('osgeo\\data\\gdal\\GDALLogoBW.svg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\GDALLogoBW.svg',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_CadastralParcel.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_CadastralParcel.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\projjson.schema.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\projjson.schema.json',
   'DATA'),
  ('osgeo\\data\\gdal\\ozi_ellips.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ozi_ellips.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\osmconf.ini',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\osmconf.ini',
   'DATA'),
  ('osgeo\\data\\gdal\\netcdf_config.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\netcdf_config.xsd',
   'DATA'),
  ('osgeo\\data\\proj\\proj.db',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\proj.db',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_CadastralBoundary.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_CadastralBoundary.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ozi_datum.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ozi_datum.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\header.dxf',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\header.dxf',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_CadastralZoning.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_CadastralZoning.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\template_tiles.mapml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\template_tiles.mapml',
   'DATA'),
  ('osgeo\\data\\gdal\\gt_datum.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\gt_datum.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_BldL.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_BldL.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\vdv452.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\vdv452.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\inspire_cp_BasicPropertyUnit.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\inspire_cp_BasicPropertyUnit.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\seed_3d.dgn',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\seed_3d.dgn',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_BldA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_BldA.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_ElevPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_ElevPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ruian_vf_ob_v1.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ruian_vf_ob_v1.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\ITRF2014',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\ITRF2014',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WA.gfs',
   'DATA'),
  ('osgeo\\data\\proj\\proj.ini',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\proj.ini',
   'DATA'),
  ('osgeo\\data\\gdal\\GDALLogoGS.svg',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\GDALLogoGS.svg',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdMgtBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdMgtBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\pdfcomposition.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pdfcomposition.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\s57objectclasses.csv',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\s57objectclasses.csv',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_SBAPt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_SBAPt.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\ogrvrt.xsd',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\ogrvrt.xsd',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_AdmBdry.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_AdmBdry.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\tms_MapML_APSTILE.json',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\tms_MapML_APSTILE.json',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_WStrA.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_WStrA.gfs',
   'DATA'),
  ('osgeo\\data\\gdal\\pds4_template.xml',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\pds4_template.xml',
   'DATA'),
  ('osgeo\\data\\proj\\nad27',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\proj\\nad27',
   'DATA'),
  ('osgeo\\data\\gdal\\jpfgdgml_RdCompt.gfs',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\osgeo\\data\\gdal\\jpfgdgml_RdCompt.gfs',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\build\\main\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1748534071,
 [('runw.exe',
   'C:\\Users\\<USER>\\Desktop\\manoeuvres '
   'simulation\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll')
