# 1``
import sys
import os
import math
import numpy as np
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QLabel,
                             QLineEdit, QPushButton, QGridLayout, QFileDialog,
                             QGraphicsView, QGraphicsScene, QGraphicsEllipseItem)
from PyQt5.QtCore import Qt, QPointF
from PyQt5.QtGui import QPixmap, QImage, QColor, QPen, QBrush
from osgeo import gdal

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("GeoTIFF Distance Calculator")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QGridLayout(self.central_widget)
        
        # Create UI elements
        self.create_widgets()
        self.setup_layout()
        
        # Initialize variables
        self.ds = None          # GDAL dataset
        self.geotransform = None
        self.image_array = None # Elevation data array
        self.points = []        # Stores selected points (lon, lat, z)
        self.display_pixmap = None  # QGraphicsPixmapItem reference

    def create_widgets(self):
        """Initialize all UI components"""
        self.btn_load = QPushButton("Load GeoTIFF")
        self.btn_clear = QPushButton("Clear Points")
        self.label_status = QLabel("Status: Load a GeoTIFF file.")
        self.label_points = QLineEdit()
        self.label_points.setReadOnly(True)
        self.label_distance = QLabel("Distance: ")
        
        # Graphics view for displaying GeoTIFF
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        
        # Connect signals
        self.btn_load.clicked.connect(self.load_geotiff)
        self.btn_clear.clicked.connect(self.clear_points)
        self.graphics_view.mousePressEvent = self.mouse_press_event

    def setup_layout(self):
        """Arrange widgets in the layout"""
        self.layout.addWidget(self.btn_load, 0, 0)
        self.layout.addWidget(self.btn_clear, 0, 1)
        self.layout.addWidget(self.label_points, 1, 0, 1, 2)
        self.layout.addWidget(self.graphics_view, 2, 0, 1, 2)
        self.layout.addWidget(self.label_status, 3, 0, 1, 2)
        self.layout.addWidget(self.label_distance, 4, 0, 1, 2)

    def load_geotiff(self):
        """Load and display GeoTIFF file"""
        filepath, _ = QFileDialog.getOpenFileName(
            self, "Open GeoTIFF", "", "GeoTIFF files (*.tif *.tiff)")
        
        if not filepath:
            return

        # Open GDAL dataset
        self.ds = gdal.Open(filepath)
        if self.ds is None:
            self.label_status.setText("Error: Could not open GeoTIFF file")
            return

        # Get geotransform and elevation data
        self.geotransform = self.ds.GetGeoTransform()
        band = self.ds.GetRasterBand(1)
        self.image_array = band.ReadAsArray()

        # Create display image
        self.create_display_image()
        self.label_status.setText(f"Loaded: {os.path.basename(filepath)}")
        self.clear_points()

    def create_display_image(self):
        """Convert elevation data to displayable image"""
        # Normalize data to 0-255
        data_min = np.nanmin(self.image_array)
        data_max = np.nanmax(self.image_array)
        normalized = (self.image_array - data_min) / (data_max - data_min) * 255
        normalized = normalized.astype(np.uint8)

        # Create QImage and pixmap
        height, width = normalized.shape
        qimage = QImage(normalized.data, width, height, width, QImage.Format_Grayscale8)
        pixmap = QPixmap.fromImage(qimage)

        # Add to scene
        self.graphics_scene.clear()
        self.display_pixmap = self.graphics_scene.addPixmap(pixmap)
        self.graphics_view.fitInView(self.display_pixmap, Qt.KeepAspectRatio)

    def mouse_press_event(self, event):
        """Handle mouse clicks on the map"""
        if not self.display_pixmap:
            return

        # Convert click position to scene coordinates
        scene_pos = self.graphics_view.mapToScene(event.pos())
        item_pos = self.display_pixmap.mapFromScene(scene_pos)
        x, y = item_pos.x(), item_pos.y()

        # Convert to pixel coordinates with rounding
        x_pixel = int(round(x))
        y_pixel = int(round(y))

        # Check bounds
        if not (0 <= x_pixel < self.image_array.shape[1] and 0 <= y_pixel < self.image_array.shape[0]):
            return

        # Convert to geographic coordinates (longitude, latitude)
        lon = self.geotransform[0] + x_pixel * self.geotransform[1] + y_pixel * self.geotransform[2]
        lat = self.geotransform[3] + x_pixel * self.geotransform[4] + y_pixel * self.geotransform[5]

        # Get elevation (handle potential NaNs)
        try:
            z = float(self.image_array[y_pixel, x_pixel])
        except (IndexError, ValueError):
            z = np.nan

        if np.isnan(z):
            self.label_status.setText("Warning: No elevation data at selected point")
            return

        # Store point and update UI
        self.points.append((lon, lat, z))
        self.update_points_display()
        self.update_display_markers()

        # Calculate distance if we have at least two points
        if len(self.points) >= 2:
            self.calculate_distance()

    def haversine(self, lon1, lat1, lon2, lat2):
        """Calculate great-circle distance between two points in meters"""
        # Convert degrees to radians
        lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])

        # Haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371000  # Earth radius in meters (approximate)
        return c * r

    def calculate_distance(self):
        """Calculate 3D distance between last two points using Haversine formula"""
        p1 = self.points[-2]
        p2 = self.points[-1]
        
        lon1, lat1, z1 = p1
        lon2, lat2, z2 = p2

        # Calculate horizontal distance
        horizontal_dist = self.haversine(lon1, lat1, lon2, lat2)
        
        # Calculate vertical difference
        vertical_diff = abs(z2 - z1)
        
        # Calculate 3D distance
        distance_3d = math.sqrt(horizontal_dist**2 + vertical_diff**2)
        
        self.label_distance.setText(
            f"3D Distance: {distance_3d:.2f} meters\n"
            f"Horizontal: {horizontal_dist:.2f} m | Vertical: {vertical_diff:.2f} m"
        )

    def update_points_display(self):
        """Update the points display textbox"""
        text = "Selected Points (Longitude, Latitude, Elevation):\n"
        for i, (lon, lat, z) in enumerate(self.points, 1):
            text += f"Point {i}: Lon={lon:.6f}, Lat={lat:.6f}, Z={z:.2f}\n"
        self.label_points.setText(text)

    def update_display_markers(self):
        """Update point markers on the map"""
        # Clear existing markers
        for item in self.graphics_scene.items():
            if isinstance(item, QGraphicsEllipseItem):
                self.graphics_scene.removeItem(item)
        
        # Add new markers
        pen = QPen(QColor(255, 0, 0))
        brush = QBrush(QColor(255, 0, 0))
        for lon, lat, _ in self.points:
            # Convert geographic to pixel coordinates
            px = (lon - self.geotransform[0]) / self.geotransform[1]
            py = (lat - self.geotransform[3]) / self.geotransform[5]
            
            # Add ellipse item
            self.graphics_scene.addEllipse(px-3, py-3, 6, 6, pen, brush)

    def clear_points(self):
        """Clear all selected points"""
        self.points = []
        self.label_points.clear()
        self.label_distance.setText("Distance: ")
        self.update_display_markers()

    def resizeEvent(self, event):
        """Handle window resize events"""
        if self.display_pixmap:
            self.graphics_view.fitInView(self.display_pixmap, Qt.KeepAspectRatio)
        super().resizeEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())